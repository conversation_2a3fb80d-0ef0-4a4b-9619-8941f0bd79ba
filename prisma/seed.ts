import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Seeding database...')

  // Create admin user
  const adminPassword = await bcrypt.hash('admin123', 12)
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Admin User',
      password: adminPassword,
      role: 'ADMIN',
    },
  })

  console.log('✅ Created admin user:', admin.email)

  // Create sample plans
  const basicPlan = await prisma.plan.upsert({
    where: { name: 'Basic' },
    update: {},
    create: {
      name: 'Basic',
      price: 99900, // ₹999 in cents
      features: {
        chatbotLimit: 1,
        kbLimit: 2,
        tokenLimit: 50000,
        kbType: 'simple',
        canUseBYOK: false,
      },
      isActive: true,
    },
  })

  const proPlan = await prisma.plan.upsert({
    where: { name: 'Pro' },
    update: {},
    create: {
      name: 'Pro',
      price: 199900, // ₹1999 in cents
      features: {
        chatbotLimit: 3,
        kbLimit: 5,
        tokenLimit: 150000,
        kbType: 'structured',
        canUseBYOK: true,
      },
      isActive: true,
    },
  })

  const enterprisePlan = await prisma.plan.upsert({
    where: { name: 'Enterprise' },
    update: {},
    create: {
      name: 'Enterprise',
      price: 499900, // ₹4999 in cents
      features: {
        chatbotLimit: 10,
        kbLimit: 20,
        tokenLimit: 500000,
        kbType: 'structured',
        canUseBYOK: true,
      },
      isActive: true,
    },
  })

  console.log('✅ Created sample plans:', [basicPlan.name, proPlan.name, enterprisePlan.name])

  // Create a test user
  const userPassword = await bcrypt.hash('user123', 12)
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Test User',
      password: userPassword,
      role: 'USER',
    },
  })

  console.log('✅ Created test user:', testUser.email)

  console.log('🎉 Seeding completed!')
  console.log('')
  console.log('Login credentials:')
  console.log('Admin: <EMAIL> / admin123')
  console.log('User: <EMAIL> / user123')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
