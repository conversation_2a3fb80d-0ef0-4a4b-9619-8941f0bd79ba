/*
  Warnings:

  - A unique constraint covering the columns `[assignedKbId]` on the table `Chatbot` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "Chatbot" ADD COLUMN     "assignedKbId" TEXT;

-- CreateTable
CREATE TABLE "KnowledgeBase" (
    "id" TEXT NOT NULL,
    "userId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "kbType" TEXT NOT NULL,
    "simpleKbText" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "KnowledgeBase_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KBChunk" (
    "id" TEXT NOT NULL,
    "knowledgeBaseId" TEXT NOT NULL,
    "content" TEXT NOT NULL,
    "content_tsvector" tsvector,
    "source" TEXT,

    CONSTRAINT "KBChunk_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KBSchoolBrand" (
    "id" TEXT NOT NULL,
    "knowledgeBaseId" TEXT NOT NULL,
    "schoolName" TEXT NOT NULL,
    "tagline" TEXT,
    "schoolType" TEXT,
    "yogaStylesTaught" TEXT[],
    "missionStatement" TEXT,
    "aboutTheSchool" TEXT,
    "founderInfo" TEXT,

    CONSTRAINT "KBSchoolBrand_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KBSchoolContact" (
    "id" TEXT NOT NULL,
    "knowledgeBaseId" TEXT NOT NULL,
    "fullAddress" TEXT,
    "googleMapsLink" TEXT,
    "howToReach" TEXT,
    "primaryPhone" TEXT,
    "whatsappNumber" TEXT,
    "primaryEmail" TEXT,
    "websiteUrl" TEXT,
    "socialMediaLinks" JSONB,

    CONSTRAINT "KBSchoolContact_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KBTeacher" (
    "id" TEXT NOT NULL,
    "knowledgeBaseId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "role" TEXT,
    "photoUrl" TEXT,
    "bio" TEXT,
    "certifications" TEXT[],

    CONSTRAINT "KBTeacher_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KBTTC" (
    "id" TEXT NOT NULL,
    "knowledgeBaseId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "certificationBody" TEXT,
    "summary" TEXT,
    "duration" TEXT,
    "skillLevel" TEXT,
    "curriculumDetails" TEXT,
    "sampleDailySchedule" TEXT,
    "priceOptions" JSONB NOT NULL,
    "inclusions" TEXT[],
    "exclusions" TEXT[],
    "upcomingDates" JSONB NOT NULL,
    "applicationProcess" TEXT,

    CONSTRAINT "KBTTC_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KBRetreat" (
    "id" TEXT NOT NULL,
    "knowledgeBaseId" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "theme" TEXT,
    "duration" TEXT,
    "intendedAudience" TEXT,
    "highlights" TEXT[],
    "priceOptions" JSONB NOT NULL,
    "upcomingDates" JSONB NOT NULL,

    CONSTRAINT "KBRetreat_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KBPolicy" (
    "id" TEXT NOT NULL,
    "knowledgeBaseId" TEXT NOT NULL,
    "codeOfConduct" TEXT,
    "paymentPolicy" TEXT,
    "cancellationAndRefundPolicy" TEXT,

    CONSTRAINT "KBPolicy_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "KBFAQ" (
    "id" TEXT NOT NULL,
    "knowledgeBaseId" TEXT NOT NULL,
    "question" TEXT NOT NULL,
    "answer" TEXT NOT NULL,

    CONSTRAINT "KBFAQ_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "kb_content_tsvector_idx" ON "KBChunk" USING GIN ("content_tsvector");

-- CreateIndex
CREATE UNIQUE INDEX "KBSchoolBrand_knowledgeBaseId_key" ON "KBSchoolBrand"("knowledgeBaseId");

-- CreateIndex
CREATE UNIQUE INDEX "KBSchoolContact_knowledgeBaseId_key" ON "KBSchoolContact"("knowledgeBaseId");

-- CreateIndex
CREATE UNIQUE INDEX "KBPolicy_knowledgeBaseId_key" ON "KBPolicy"("knowledgeBaseId");

-- CreateIndex
CREATE UNIQUE INDEX "Chatbot_assignedKbId_key" ON "Chatbot"("assignedKbId");

-- AddForeignKey
ALTER TABLE "Chatbot" ADD CONSTRAINT "Chatbot_assignedKbId_fkey" FOREIGN KEY ("assignedKbId") REFERENCES "KnowledgeBase"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KnowledgeBase" ADD CONSTRAINT "KnowledgeBase_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KBChunk" ADD CONSTRAINT "KBChunk_knowledgeBaseId_fkey" FOREIGN KEY ("knowledgeBaseId") REFERENCES "KnowledgeBase"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KBSchoolBrand" ADD CONSTRAINT "KBSchoolBrand_knowledgeBaseId_fkey" FOREIGN KEY ("knowledgeBaseId") REFERENCES "KnowledgeBase"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KBSchoolContact" ADD CONSTRAINT "KBSchoolContact_knowledgeBaseId_fkey" FOREIGN KEY ("knowledgeBaseId") REFERENCES "KnowledgeBase"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KBTeacher" ADD CONSTRAINT "KBTeacher_knowledgeBaseId_fkey" FOREIGN KEY ("knowledgeBaseId") REFERENCES "KnowledgeBase"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KBTTC" ADD CONSTRAINT "KBTTC_knowledgeBaseId_fkey" FOREIGN KEY ("knowledgeBaseId") REFERENCES "KnowledgeBase"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KBRetreat" ADD CONSTRAINT "KBRetreat_knowledgeBaseId_fkey" FOREIGN KEY ("knowledgeBaseId") REFERENCES "KnowledgeBase"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KBPolicy" ADD CONSTRAINT "KBPolicy_knowledgeBaseId_fkey" FOREIGN KEY ("knowledgeBaseId") REFERENCES "KnowledgeBase"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "KBFAQ" ADD CONSTRAINT "KBFAQ_knowledgeBaseId_fkey" FOREIGN KEY ("knowledgeBaseId") REFERENCES "KnowledgeBase"("id") ON DELETE CASCADE ON UPDATE CASCADE;
