# ====================================================================================
#  Definitive Docker Compose File for Local Development with pgvector
# ====================================================================================

version: '3.8'

services:
  db:
    # This is the critical change. Instead of pulling a pre-made image from Docker Hub,
    # 'build: .' tells docker-compose to look for a 'Dockerfile' in the current
    # directory and build a custom image from it.
    build: .

    # We give our custom-built container a memorable name for easy reference.
    container_name: yogabot_db_dev

    # 'restart: always' is a convenience for local development.
    # It means if your computer restarts or Docker restarts, this database
    # container will automatically start back up for you.
    restart: always

    # 'environment' lets you set up the internal configuration of the Postgres software.
    # These are the credentials the database will use for itself.
    environment:
      POSTGRES_USER: myuser            # Sets the username for the database.
      POSTGRES_PASSWORD: mypassword      # Sets the password for that user.
      POSTGRES_DB: yogabot_dev         # Creates an initial empty database with this name.

    # 'ports' connects your computer to the container.
    # It maps 'Host Port : Container Port'.
    #  - '54321': The port on your actual laptop that you will connect to.
    #  - '5432': The default port that PostgreSQL runs on inside the container.
    ports:
      - '54321:5432'

    # 'volumes' makes your data permanent and runs initialization scripts.
    volumes:
      # This first volume ensures your database data persists even if the container is removed.
      - postgres_data:/var/lib/postgresql/data
      
      # This second volume is the key to automating the extension creation.
      # It maps the folder you created on your computer into a special folder
      # inside the container that Postgres checks on its first startup.
      - ./docker-entrypoint-initdb.d:/docker-entrypoint-initdb.d

# This top-level 'volumes' section formally defines the named volume from above.
volumes:
  postgres_data: