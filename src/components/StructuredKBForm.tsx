"use client"

import { useState, useEffect } from 'react'
import { Save, Loader2, CheckCircle, AlertCircle, Database, Plus, Trash2 } from 'lucide-react'

interface Chatbot {
  id: string
  _count: {
    knowledgeChunks: number
  }
}

interface StructuredKBFormProps {
  chatbot: Chatbot
}

interface SchoolBrand {
  schoolName: string
  tagline?: string
  schoolType?: string
  yogaStylesTaught: string[]
  missionStatement?: string
  aboutTheSchool?: string
  founderInfo?: string
}

interface SchoolContact {
  fullAddress?: string
  googleMapsLink?: string
  howToReach?: string
  primaryPhone?: string
  whatsappNumber?: string
  primaryEmail?: string
  websiteUrl?: string
  socialMediaLinks: Array<{ platform: string; url: string }>
}

interface Teacher {
  name: string
  role?: string
  photoUrl?: string
  bio?: string
  certifications: string[]
}

interface FAQ {
  question: string
  answer: string
}

interface StructuredData {
  schoolBrand?: SchoolBrand
  schoolContact?: SchoolContact
  teachers: Teacher[]
  faqs: FAQ[]
  policies?: {
    codeOfConduct?: string
    paymentPolicy?: string
    cancellationAndRefundPolicy?: string
  }
}

export default function StructuredKBForm({ chatbot }: StructuredKBFormProps) {
  const [data, setData] = useState<StructuredData>({
    teachers: [],
    faqs: []
  })
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)
  const [activeTab, setActiveTab] = useState('school')

  // Load initial data
  useEffect(() => {
    const loadStructuredData = async () => {
      setIsLoading(true)
      try {
        const response = await fetch(`/api/kb/structured?chatbotId=${chatbot.id}`)
        if (response.ok) {
          const result = await response.json()
          setData({
            schoolBrand: result.schoolBrand || undefined,
            schoolContact: result.schoolContact || undefined,
            teachers: result.teachers || [],
            faqs: result.faqs || [],
            policies: result.policies || undefined
          })
        }
      } catch (error) {
        console.error('Error loading structured data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadStructuredData()
  }, [chatbot.id])

  const handleSave = async () => {
    setIsSaving(true)
    setMessage(null)

    try {
      const response = await fetch('/api/kb/structured', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatbotId: chatbot.id,
          ...data
        })
      })

      const result = await response.json()

      if (response.ok) {
        setMessage({ type: 'success', text: result.message || 'Knowledge base updated successfully!' })
      } else {
        setMessage({ type: 'error', text: result.error || 'Failed to update knowledge base' })
      }
    } catch (error) {
      console.error('Error saving structured KB:', error)
      setMessage({ type: 'error', text: 'Network error. Please try again.' })
    } finally {
      setIsSaving(false)
    }
  }

  const addTeacher = () => {
    setData(prev => ({
      ...prev,
      teachers: [...prev.teachers, { name: '', certifications: [] }]
    }))
  }

  const removeTeacher = (index: number) => {
    setData(prev => ({
      ...prev,
      teachers: prev.teachers.filter((_, i) => i !== index)
    }))
  }

  const updateTeacher = (index: number, field: keyof Teacher, value: any) => {
    setData(prev => ({
      ...prev,
      teachers: prev.teachers.map((teacher, i) => 
        i === index ? { ...teacher, [field]: value } : teacher
      )
    }))
  }

  const addFAQ = () => {
    setData(prev => ({
      ...prev,
      faqs: [...prev.faqs, { question: '', answer: '' }]
    }))
  }

  const removeFAQ = (index: number) => {
    setData(prev => ({
      ...prev,
      faqs: prev.faqs.filter((_, i) => i !== index)
    }))
  }

  const updateFAQ = (index: number, field: keyof FAQ, value: string) => {
    setData(prev => ({
      ...prev,
      faqs: prev.faqs.map((faq, i) => 
        i === index ? { ...faq, [field]: value } : faq
      )
    }))
  }

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading structured knowledge base...</span>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'school', label: 'School Info', icon: Database },
    { id: 'teachers', label: 'Teachers', icon: Plus },
    { id: 'faqs', label: 'FAQs', icon: Plus },
    { id: 'policies', label: 'Policies', icon: Database }
  ]

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-3 mb-6">
        <Database className="w-6 h-6 text-green-600" />
        <h3 className="text-xl font-semibold text-gray-900">Structured Knowledge Base</h3>
        <span className="text-sm text-gray-500">
          ({chatbot._count.knowledgeChunks} chunks processed)
        </span>
      </div>

      {/* Message Display */}
      {message && (
        <div className={`mb-4 p-4 rounded-lg flex items-center space-x-2 ${
          message.type === 'success' 
            ? 'bg-green-50 border border-green-200 text-green-800' 
            : 'bg-red-50 border border-red-200 text-red-800'
        }`}>
          {message.type === 'success' ? (
            <CheckCircle className="w-5 h-5" />
          ) : (
            <AlertCircle className="w-5 h-5" />
          )}
          <span>{message.text}</span>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center space-x-2">
                <tab.icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </div>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content - This will be continued in the next part */}
      <div className="space-y-6">
        {/* School Info Tab */}
        {activeTab === 'school' && (
          <div className="space-y-6">
            <h4 className="text-lg font-medium text-gray-900">School Information</h4>

            {/* School Brand Section */}
            <div className="space-y-4">
              <h5 className="font-medium text-gray-800">Brand Information</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">School Name *</label>
                  <input
                    type="text"
                    value={data.schoolBrand?.schoolName || ''}
                    onChange={(e) => setData(prev => ({
                      ...prev,
                      schoolBrand: { ...prev.schoolBrand, schoolName: e.target.value } as SchoolBrand
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Your Yoga School Name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Tagline</label>
                  <input
                    type="text"
                    value={data.schoolBrand?.tagline || ''}
                    onChange={(e) => setData(prev => ({
                      ...prev,
                      schoolBrand: { ...prev.schoolBrand, tagline: e.target.value } as SchoolBrand
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Your school's tagline"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Mission Statement</label>
                <textarea
                  value={data.schoolBrand?.missionStatement || ''}
                  onChange={(e) => setData(prev => ({
                    ...prev,
                    schoolBrand: { ...prev.schoolBrand, missionStatement: e.target.value } as SchoolBrand
                  }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Your school's mission statement"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">About the School</label>
                <textarea
                  value={data.schoolBrand?.aboutTheSchool || ''}
                  onChange={(e) => setData(prev => ({
                    ...prev,
                    schoolBrand: { ...prev.schoolBrand, aboutTheSchool: e.target.value } as SchoolBrand
                  }))}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Detailed description of your school"
                />
              </div>
            </div>

            {/* Contact Information Section */}
            <div className="space-y-4">
              <h5 className="font-medium text-gray-800">Contact Information</h5>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Primary Phone</label>
                  <input
                    type="tel"
                    value={data.schoolContact?.primaryPhone || ''}
                    onChange={(e) => setData(prev => ({
                      ...prev,
                      schoolContact: { ...prev.schoolContact, primaryPhone: e.target.value, socialMediaLinks: prev.schoolContact?.socialMediaLinks || [] } as SchoolContact
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="+****************"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Primary Email</label>
                  <input
                    type="email"
                    value={data.schoolContact?.primaryEmail || ''}
                    onChange={(e) => setData(prev => ({
                      ...prev,
                      schoolContact: { ...prev.schoolContact, primaryEmail: e.target.value, socialMediaLinks: prev.schoolContact?.socialMediaLinks || [] } as SchoolContact
                    }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Full Address</label>
                <textarea
                  value={data.schoolContact?.fullAddress || ''}
                  onChange={(e) => setData(prev => ({
                    ...prev,
                    schoolContact: { ...prev.schoolContact, fullAddress: e.target.value, socialMediaLinks: prev.schoolContact?.socialMediaLinks || [] } as SchoolContact
                  }))}
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="123 Yoga Street, City, State, ZIP"
                />
              </div>
            </div>
          </div>
        )}

        {/* Teachers Tab */}
        {activeTab === 'teachers' && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h4 className="text-lg font-medium text-gray-900">Teachers</h4>
              <button
                onClick={addTeacher}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
              >
                <Plus className="w-4 h-4 mr-1" />
                Add Teacher
              </button>
            </div>

            {data.teachers.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p>No teachers added yet. Click "Add Teacher" to get started.</p>
              </div>
            ) : (
              <div className="space-y-6">
                {data.teachers.map((teacher, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-4">
                      <h5 className="font-medium text-gray-800">Teacher {index + 1}</h5>
                      <button
                        onClick={() => removeTeacher(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Name *</label>
                        <input
                          type="text"
                          value={teacher.name}
                          onChange={(e) => updateTeacher(index, 'name', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Teacher's full name"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
                        <input
                          type="text"
                          value={teacher.role || ''}
                          onChange={(e) => updateTeacher(index, 'role', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Lead Instructor, Assistant, etc."
                        />
                      </div>
                    </div>

                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Bio</label>
                      <textarea
                        value={teacher.bio || ''}
                        onChange={(e) => updateTeacher(index, 'bio', e.target.value)}
                        rows={3}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Teacher's background, experience, and specialties"
                      />
                    </div>

                    <div className="mt-4">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Certifications</label>
                      <input
                        type="text"
                        value={teacher.certifications.join(', ')}
                        onChange={(e) => updateTeacher(index, 'certifications', e.target.value.split(',').map(s => s.trim()).filter(s => s))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="RYT-200, RYT-500, etc. (comma separated)"
                      />
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* FAQs Tab */}
        {activeTab === 'faqs' && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h4 className="text-lg font-medium text-gray-900">Frequently Asked Questions</h4>
              <button
                onClick={addFAQ}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
              >
                <Plus className="w-4 h-4 mr-1" />
                Add FAQ
              </button>
            </div>

            {data.faqs.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <p>No FAQs added yet. Click "Add FAQ" to get started.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {data.faqs.map((faq, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex justify-between items-center mb-4">
                      <h5 className="font-medium text-gray-800">FAQ {index + 1}</h5>
                      <button
                        onClick={() => removeFAQ(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Question *</label>
                        <input
                          type="text"
                          value={faq.question}
                          onChange={(e) => updateFAQ(index, 'question', e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="What is your question?"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Answer *</label>
                        <textarea
                          value={faq.answer}
                          onChange={(e) => updateFAQ(index, 'answer', e.target.value)}
                          rows={3}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="Provide a detailed answer"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Policies Tab */}
        {activeTab === 'policies' && (
          <div className="space-y-6">
            <h4 className="text-lg font-medium text-gray-900">Policies</h4>

            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Code of Conduct</label>
                <textarea
                  value={data.policies?.codeOfConduct || ''}
                  onChange={(e) => setData(prev => ({
                    ...prev,
                    policies: { ...prev.policies, codeOfConduct: e.target.value }
                  }))}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Your school's code of conduct and behavioral expectations"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Payment Policy</label>
                <textarea
                  value={data.policies?.paymentPolicy || ''}
                  onChange={(e) => setData(prev => ({
                    ...prev,
                    policies: { ...prev.policies, paymentPolicy: e.target.value }
                  }))}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Payment terms, accepted methods, late fees, etc."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Cancellation and Refund Policy</label>
                <textarea
                  value={data.policies?.cancellationAndRefundPolicy || ''}
                  onChange={(e) => setData(prev => ({
                    ...prev,
                    policies: { ...prev.policies, cancellationAndRefundPolicy: e.target.value }
                  }))}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Cancellation deadlines, refund conditions, etc."
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Save Button */}
      <div className="flex justify-end mt-8 pt-6 border-t border-gray-200">
        <button
          onClick={handleSave}
          disabled={isSaving}
          className={`inline-flex items-center px-6 py-3 rounded-lg font-medium transition-colors ${
            isSaving
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-green-600 text-white hover:bg-green-700'
          }`}
        >
          {isSaving ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Saving & Processing...
            </>
          ) : (
            <>
              <Save className="w-4 h-4 mr-2" />
              Save & Process Knowledge Base
            </>
          )}
        </button>
      </div>
    </div>
  )
}
