"use client"

import { useState } from 'react'
import { Bot, ChevronDown, FileText, Database } from 'lucide-react'
import SimpleKBForm from '@/components/SimpleKBForm'
import StructuredKBForm from '@/components/StructuredKBForm'

interface Chatbot {
  id: string
  approvedDomain: string
  systemPrompt: string | null
  kbTypeOverride: string | null
  simpleKbCharacterLimit: number | null
  simpleKbText: string | null
  user: {
    subscription: {
      plan: {
        name: string
        features: {
          kbType: string
          [key: string]: any
        }
      }
    } | null
  }
  _count: {
    knowledgeChunks: number
  }
}

interface KnowledgeBaseClientProps {
  chatbots: Chatbot[]
}

export default function KnowledgeBaseClient({ chatbots }: KnowledgeBaseClientProps) {
  const [selectedChatbotId, setSelectedChatbotId] = useState<string>(chatbots[0]?.id || '')
  
  const selectedChatbot = chatbots.find(bot => bot.id === selectedChatbotId)
  
  if (!selectedChatbot) {
    return <div>No chatbot selected</div>
  }

  // Determine effective KB type
  const effectiveKbType = selectedChatbot.kbTypeOverride || 
    selectedChatbot.user.subscription?.plan.features.kbType || 
    'simple'

  return (
    <div className="space-y-6">
      {/* Chatbot Selector */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">Select Chatbot</h2>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Bot className="w-4 h-4" />
            <span>{chatbots.length} chatbot{chatbots.length !== 1 ? 's' : ''}</span>
          </div>
        </div>
        
        <div className="relative">
          <select
            value={selectedChatbotId}
            onChange={(e) => setSelectedChatbotId(e.target.value)}
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
          >
            {chatbots.map((chatbot) => (
              <option key={chatbot.id} value={chatbot.id}>
                {chatbot.approvedDomain} ({chatbot.user.subscription?.plan.name || 'No Plan'})
              </option>
            ))}
          </select>
          <ChevronDown className="absolute right-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" />
        </div>
      </div>

      {/* KB Type Info */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          {effectiveKbType === 'simple' ? (
            <FileText className="w-6 h-6 text-blue-600" />
          ) : (
            <Database className="w-6 h-6 text-green-600" />
          )}
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {effectiveKbType === 'simple' ? 'Simple Text Knowledge Base' : 'Structured Knowledge Base'}
            </h3>
            <p className="text-sm text-gray-600">
              {effectiveKbType === 'simple' 
                ? 'Direct text input with character limits'
                : 'Structured forms with Full-Text Search capabilities'
              }
            </p>
          </div>
        </div>

        {/* KB Type Override Info */}
        {selectedChatbot.kbTypeOverride && (
          <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 mb-4">
            <p className="text-sm text-amber-800">
              <strong>Admin Override:</strong> This chatbot is configured to use {selectedChatbot.kbTypeOverride} KB type, 
              overriding your plan's default ({selectedChatbot.user.subscription?.plan.features.kbType}).
            </p>
          </div>
        )}

        {/* Character Limit Info for Simple KB */}
        {effectiveKbType === 'simple' && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p className="text-sm text-blue-800">
              <strong>Character Limit:</strong> {
                selectedChatbot.simpleKbCharacterLimit?.toLocaleString() || '10,000'
              } characters
            </p>
          </div>
        )}

        {/* Knowledge Chunks Info for Structured KB */}
        {effectiveKbType === 'structured' && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <p className="text-sm text-green-800">
              <strong>Knowledge Chunks:</strong> {selectedChatbot._count.knowledgeChunks} processed chunks
            </p>
          </div>
        )}
      </div>

      {/* Dynamic KB Form */}
      {effectiveKbType === 'simple' ? (
        <SimpleKBForm chatbot={selectedChatbot} />
      ) : (
        <StructuredKBForm chatbot={selectedChatbot} />
      )}
    </div>
  )
}
