"use client"

import { useState, useEffect } from 'react'
import { Save, Loader2, CheckCircle, AlertCircle, FileText } from 'lucide-react'

interface Chatbot {
  id: string
  simpleKbText: string | null
  simpleKbCharacterLimit: number | null
  user: {
    subscription: {
      plan: {
        features: {
          [key: string]: any
        }
      }
    } | null
  }
}

interface SimpleKBFormProps {
  chatbot: Chatbot
}

export default function SimpleKBForm({ chatbot }: SimpleKBFormProps) {
  const [text, setText] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)
  const [characterLimit, setCharacterLimit] = useState(10000)

  // Load initial data
  useEffect(() => {
    const loadKBData = async () => {
      setIsLoading(true)
      try {
        const response = await fetch(`/api/kb/simple?chatbotId=${chatbot.id}`)
        if (response.ok) {
          const data = await response.json()
          setText(data.text || '')
          setCharacterLimit(data.characterLimit || 10000)
        } else {
          // Fallback to chatbot data
          setText(chatbot.simpleKbText || '')
          setCharacterLimit(chatbot.simpleKbCharacterLimit || 10000)
        }
      } catch (error) {
        console.error('Error loading KB data:', error)
        setText(chatbot.simpleKbText || '')
        setCharacterLimit(chatbot.simpleKbCharacterLimit || 10000)
      } finally {
        setIsLoading(false)
      }
    }

    loadKBData()
  }, [chatbot.id, chatbot.simpleKbText, chatbot.simpleKbCharacterLimit])

  const handleSave = async () => {
    setIsSaving(true)
    setMessage(null)

    try {
      const response = await fetch('/api/kb/simple', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chatbotId: chatbot.id,
          text: text
        })
      })

      const data = await response.json()

      if (response.ok) {
        setMessage({ type: 'success', text: data.message || 'Knowledge base updated successfully!' })
        // Update character limit if it changed
        if (data.characterLimit) {
          setCharacterLimit(data.characterLimit)
        }
      } else {
        setMessage({ type: 'error', text: data.error || 'Failed to update knowledge base' })
      }
    } catch (error) {
      console.error('Error saving KB:', error)
      setMessage({ type: 'error', text: 'Network error. Please try again.' })
    } finally {
      setIsSaving(false)
    }
  }

  const currentLength = text.length
  const isOverLimit = currentLength > characterLimit
  const percentUsed = (currentLength / characterLimit) * 100

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-blue-600" />
          <span className="ml-2 text-gray-600">Loading knowledge base...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
      <div className="flex items-center space-x-3 mb-6">
        <FileText className="w-6 h-6 text-blue-600" />
        <h3 className="text-xl font-semibold text-gray-900">Simple Knowledge Base</h3>
      </div>

      {/* Message Display */}
      {message && (
        <div className={`mb-4 p-4 rounded-lg flex items-center space-x-2 ${
          message.type === 'success' 
            ? 'bg-green-50 border border-green-200 text-green-800' 
            : 'bg-red-50 border border-red-200 text-red-800'
        }`}>
          {message.type === 'success' ? (
            <CheckCircle className="w-5 h-5" />
          ) : (
            <AlertCircle className="w-5 h-5" />
          )}
          <span>{message.text}</span>
        </div>
      )}

      {/* Character Count Display */}
      <div className="mb-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-sm font-medium text-gray-700">Character Count</span>
          <span className={`text-sm font-mono ${
            isOverLimit ? 'text-red-600' : percentUsed > 80 ? 'text-amber-600' : 'text-gray-600'
          }`}>
            {currentLength.toLocaleString()} / {characterLimit.toLocaleString()}
          </span>
        </div>
        
        {/* Progress Bar */}
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full transition-all duration-300 ${
              isOverLimit ? 'bg-red-500' : percentUsed > 80 ? 'bg-amber-500' : 'bg-blue-500'
            }`}
            style={{ width: `${Math.min(percentUsed, 100)}%` }}
          />
        </div>
        
        {isOverLimit && (
          <p className="text-sm text-red-600 mt-1">
            Text exceeds character limit by {(currentLength - characterLimit).toLocaleString()} characters
          </p>
        )}
      </div>

      {/* Textarea */}
      <div className="mb-6">
        <label htmlFor="kb-text" className="block text-sm font-medium text-gray-700 mb-2">
          Knowledge Base Content
        </label>
        <textarea
          id="kb-text"
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder="Enter your business information, FAQs, policies, and any other knowledge you want your chatbot to have access to..."
          className={`w-full h-64 px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${
            isOverLimit ? 'border-red-300' : 'border-gray-300'
          }`}
        />
        <p className="text-sm text-gray-500 mt-2">
          This text will be directly included in your chatbot's context when responding to user questions.
        </p>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={handleSave}
          disabled={isSaving || isOverLimit}
          className={`inline-flex items-center px-6 py-3 rounded-lg font-medium transition-colors ${
            isSaving || isOverLimit
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-600 text-white hover:bg-blue-700'
          }`}
        >
          {isSaving ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="w-4 h-4 mr-2" />
              Save Knowledge Base
            </>
          )}
        </button>
      </div>
    </div>
  )
}
