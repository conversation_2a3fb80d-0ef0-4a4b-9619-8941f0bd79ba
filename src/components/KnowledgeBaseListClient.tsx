"use client"

import { useState } from 'react'
import { Database, Plus, <PERSON>ting<PERSON>, Bo<PERSON>, FileText, Layers } from 'lucide-react'
import Link from 'next/link'

interface KnowledgeBase {
  id: string
  name: string
  description: string | null
  kbType: string
  assignedChatbot: {
    id: string
    approvedDomain: string
  } | null
  _count: {
    knowledgeChunks: number
  }
  createdAt: string
}

interface User {
  id: string
  subscription: {
    plan: {
      name: string
      features: {
        kbLimit: number
        kbType: string
        [key: string]: any
      }
    }
  } | null
  knowledgeBases: KnowledgeBase[]
}

interface KnowledgeBaseListClientProps {
  user: User
}

export default function KnowledgeBaseListClient({ user }: KnowledgeBaseListClientProps) {
  const [isCreating, setIsCreating] = useState(false)
  const [showCreateModal, setShowCreateModal] = useState(false)

  const kbLimit = user.subscription?.plan.features.kbLimit || 1
  const planKbType = user.subscription?.plan.features.kbType || 'simple'
  const canCreateMore = user.knowledgeBases.length < kbLimit

  const handleCreateKB = () => {
    setShowCreateModal(true)
  }

  return (
    <div className="space-y-6">
      {/* Stats and Create Button */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center space-x-3">
              <Database className="w-8 h-8 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Total Knowledge Bases</p>
                <p className="text-2xl font-bold text-gray-900">
                  {user.knowledgeBases.length} / {kbLimit}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center space-x-3">
              <Bot className="w-8 h-8 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Assigned to Chatbots</p>
                <p className="text-2xl font-bold text-gray-900">
                  {user.knowledgeBases.filter(kb => kb.assignedChatbot).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {canCreateMore && (
          <button
            onClick={handleCreateKB}
            disabled={isCreating}
            className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Knowledge Base
          </button>
        )}
      </div>

      {/* Plan Limit Info */}
      {!canCreateMore && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <Database className="w-5 h-5 text-amber-600" />
            <p className="text-amber-800">
              <strong>Knowledge Base Limit Reached:</strong> Your {user.subscription?.plan.name || 'current'} plan allows {kbLimit} knowledge base{kbLimit !== 1 ? 's' : ''}. 
              <Link href="/dashboard/settings" className="ml-1 underline hover:no-underline">
                Upgrade your plan
              </Link> to create more knowledge bases.
            </p>
          </div>
        </div>
      )}

      {/* Plan KB Type Info */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center space-x-2">
          <Layers className="w-5 h-5 text-blue-600" />
          <p className="text-blue-800">
            <strong>Your Plan:</strong> {user.subscription?.plan.name || 'Basic'} - 
            {planKbType === 'simple' ? (
              <span> You can create Simple Knowledge Bases with direct text input.</span>
            ) : (
              <span> You can create both Simple and Structured Knowledge Bases with advanced features.</span>
            )}
          </p>
        </div>
      </div>

      {/* Knowledge Bases Grid */}
      {user.knowledgeBases.length === 0 ? (
        <div className="text-center py-12">
          <Database className="mx-auto h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Knowledge Bases Yet</h3>
          <p className="text-gray-600 mb-6">
            Create your first knowledge base to provide context and information for your chatbots.
          </p>
          {canCreateMore && (
            <button
              onClick={handleCreateKB}
              disabled={isCreating}
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Your First Knowledge Base
            </button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {user.knowledgeBases.map((kb) => (
            <div
              key={kb.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
            >
              {/* KB Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                    kb.kbType === 'simple' ? 'bg-blue-100' : 'bg-green-100'
                  }`}>
                    {kb.kbType === 'simple' ? (
                      <FileText className={`w-5 h-5 ${kb.kbType === 'simple' ? 'text-blue-600' : 'text-green-600'}`} />
                    ) : (
                      <Layers className="w-5 h-5 text-green-600" />
                    )}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 truncate">
                      {kb.name}
                    </h3>
                    <p className="text-sm text-gray-500 capitalize">
                      {kb.kbType} KB • {kb._count.knowledgeChunks} chunks
                    </p>
                  </div>
                </div>
              </div>

              {/* Description */}
              {kb.description && (
                <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                  {kb.description}
                </p>
              )}

              {/* Assignment Status */}
              <div className="mb-4">
                {kb.assignedChatbot ? (
                  <div className="flex items-center space-x-2 text-sm">
                    <Bot className="w-4 h-4 text-blue-600" />
                    <span className="text-blue-700 font-medium">
                      Assigned to: {kb.assignedChatbot.approvedDomain}
                    </span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Bot className="w-4 h-4" />
                    <span>Not assigned to any chatbot</span>
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                <Link
                  href={`/dashboard/kb/${kb.id}`}
                  className="inline-flex items-center px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  <Settings className="w-3 h-3 mr-1" />
                  Manage
                </Link>
                
                <span className="text-xs text-gray-400">
                  Created {new Date(kb.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Create KB Modal - Placeholder for now */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Create Knowledge Base</h3>
            <p className="text-gray-600 mb-6">
              Knowledge base creation functionality will be implemented in the next phase.
            </p>
            <div className="flex justify-end">
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
