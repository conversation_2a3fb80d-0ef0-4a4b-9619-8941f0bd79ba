"use client"

import { useState } from 'react'
import { Bo<PERSON>, Plus, <PERSON>ting<PERSON>, ExternalLink, Trash2, Database } from 'lucide-react'
import Link from 'next/link'

interface Chatbot {
  id: string
  approvedDomain: string
  assignedKb: {
    id: string
    name: string
    kbType: string
  } | null
  _count: {
    chatSessions: number
  }
}

interface User {
  id: string
  email: string
  name: string | null
  subscription: {
    plan: {
      name: string
      features: {
        chatbotLimit: number
        [key: string]: any
      }
    }
  } | null
  chatbots: Chatbot[]
}

interface ChatbotsClientProps {
  user: User
}

export default function ChatbotsClient({ user }: ChatbotsClientProps) {
  const [isCreating, setIsCreating] = useState(false)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [formData, setFormData] = useState({
    approvedDomain: '',
    systemPrompt: ''
  })

  const chatbotLimit = user.subscription?.plan.features.chatbotLimit || 1
  const canCreateMore = user.chatbots.length < chatbotLimit

  const handleCreateChatbot = () => {
    setShowCreateModal(true)
  }

  const handleSubmitCreate = async () => {
    if (!formData.approvedDomain.trim()) {
      alert('Please enter an approved domain')
      return
    }

    setIsCreating(true)
    try {
      const response = await fetch('/api/user/chatbots', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          approvedDomain: formData.approvedDomain.trim(),
          systemPrompt: formData.systemPrompt.trim() || undefined
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to create chatbot')
      }

      // Refresh the page to show the new chatbot
      window.location.reload()
    } catch (error) {
      console.error('Error creating chatbot:', error)
      alert(error instanceof Error ? error.message : 'Failed to create chatbot. Please try again.')
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Stats and Create Button */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center space-x-3">
              <Bot className="w-8 h-8 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Chatbots</p>
                <p className="text-2xl font-bold text-gray-900">
                  {user.chatbots.length} / {chatbotLimit}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
            <div className="flex items-center space-x-3">
              <Database className="w-8 h-8 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">With Knowledge Base</p>
                <p className="text-2xl font-bold text-gray-900">
                  {user.chatbots.filter(bot => bot.assignedKb).length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {canCreateMore && (
          <button
            onClick={handleCreateChatbot}
            disabled={isCreating}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Chatbot
          </button>
        )}
      </div>

      {/* Plan Limit Info */}
      {!canCreateMore && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <Bot className="w-5 h-5 text-amber-600" />
            <p className="text-amber-800">
              <strong>Chatbot Limit Reached:</strong> Your {user.subscription?.plan.name || 'current'} plan allows {chatbotLimit} chatbot{chatbotLimit !== 1 ? 's' : ''}. 
              <Link href="/dashboard/settings" className="ml-1 underline hover:no-underline">
                Upgrade your plan
              </Link> to create more chatbots.
            </p>
          </div>
        </div>
      )}

      {/* Chatbots Grid */}
      {user.chatbots.length === 0 ? (
        <div className="text-center py-12">
          <Bot className="mx-auto h-16 w-16 text-gray-400 mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Chatbots Yet</h3>
          <p className="text-gray-600 mb-6">
            Create your first chatbot to get started with automated customer support.
          </p>
          {canCreateMore && (
            <button
              onClick={handleCreateChatbot}
              disabled={isCreating}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
            >
              <Plus className="w-4 h-4 mr-2" />
              Create Your First Chatbot
            </button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {user.chatbots.map((chatbot) => (
            <div
              key={chatbot.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow"
            >
              {/* Chatbot Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Bot className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 truncate">
                      {chatbot.approvedDomain}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {chatbot._count.chatSessions} conversation{chatbot._count.chatSessions !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>
              </div>

              {/* Knowledge Base Status */}
              <div className="mb-4">
                {chatbot.assignedKb ? (
                  <div className="flex items-center space-x-2 text-sm">
                    <Database className="w-4 h-4 text-green-600" />
                    <span className="text-green-700 font-medium">{chatbot.assignedKb.name}</span>
                    <span className="text-gray-500">({chatbot.assignedKb.kbType})</span>
                  </div>
                ) : (
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Database className="w-4 h-4" />
                    <span>No knowledge base assigned</span>
                  </div>
                )}
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                <div className="flex items-center space-x-2">
                  <Link
                    href={`/dashboard/chatbots/${chatbot.id}`}
                    className="inline-flex items-center px-3 py-1.5 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                  >
                    <Settings className="w-3 h-3 mr-1" />
                    Settings
                  </Link>
                  
                  <a
                    href={`/widget/${chatbot.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center px-3 py-1.5 text-sm bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 transition-colors"
                  >
                    <ExternalLink className="w-3 h-3 mr-1" />
                    Test
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Create Chatbot Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Create New Chatbot</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Approved Domain *
                </label>
                <input
                  type="url"
                  value={formData.approvedDomain}
                  onChange={(e) => setFormData(prev => ({ ...prev, approvedDomain: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="https://yourwebsite.com"
                  disabled={isCreating}
                />
                <p className="text-sm text-gray-500 mt-1">
                  The domain where this chatbot widget can be embedded
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  System Prompt (Optional)
                </label>
                <textarea
                  value={formData.systemPrompt}
                  onChange={(e) => setFormData(prev => ({ ...prev, systemPrompt: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="You are a helpful assistant for..."
                  disabled={isCreating}
                />
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowCreateModal(false)
                  setFormData({ approvedDomain: '', systemPrompt: '' })
                }}
                disabled={isCreating}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmitCreate}
                disabled={isCreating || !formData.approvedDomain.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {isCreating ? 'Creating...' : 'Create Chatbot'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
