"use client"

import { useState } from 'react'
import { Bot, Database, Save, Trash2, ExternalLink, AlertTriangle } from 'lucide-react'
import { useRouter } from 'next/navigation'

interface KnowledgeBase {
  id: string
  name: string
  kbType: string
  description: string | null
  assignedChatbot: {
    id: string
    approvedDomain: string
  } | null
}

interface Chatbot {
  id: string
  approvedDomain: string
  systemPrompt: string | null
  widgetConfig: any
  assignedKb: {
    id: string
    name: string
    kbType: string
    description: string | null
  } | null
  _count: {
    chatSessions: number
  }
}

interface ChatbotSettingsClientProps {
  chatbot: Chatbot
  knowledgeBases: KnowledgeBase[]
}

export default function ChatbotSettingsClient({ chatbot, knowledgeBases }: ChatbotSettingsClientProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false)
  const [formData, setFormData] = useState({
    approvedDomain: chatbot.approvedDomain,
    systemPrompt: chatbot.systemPrompt || '',
    assignedKbId: chatbot.assignedKb?.id || ''
  })

  const handleSave = async () => {
    setIsLoading(true)
    try {
      // Update chatbot basic settings
      const response = await fetch(`/api/user/chatbots/${chatbot.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          approvedDomain: formData.approvedDomain,
          systemPrompt: formData.systemPrompt
        })
      })

      if (!response.ok) {
        throw new Error('Failed to update chatbot')
      }

      // Update KB assignment if changed
      if (formData.assignedKbId !== (chatbot.assignedKb?.id || '')) {
        const kbResponse = await fetch(`/api/user/chatbots/${chatbot.id}/assign-kb`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            knowledgeBaseId: formData.assignedKbId || null
          })
        })

        if (!kbResponse.ok) {
          throw new Error('Failed to update KB assignment')
        }
      }

      // Refresh the page to show updated data
      router.refresh()
    } catch (error) {
      console.error('Error updating chatbot:', error)
      alert('Failed to update chatbot. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDelete = async () => {
    setIsLoading(true)
    try {
      const response = await fetch(`/api/user/chatbots/${chatbot.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) {
        throw new Error('Failed to delete chatbot')
      }

      router.push('/dashboard/chatbots')
    } catch (error) {
      console.error('Error deleting chatbot:', error)
      alert('Failed to delete chatbot. Please try again.')
      setIsLoading(false)
    }
  }

  // Filter available KBs (exclude ones assigned to other chatbots)
  const availableKBs = knowledgeBases.filter(kb => 
    !kb.assignedChatbot || kb.assignedChatbot.id === chatbot.id
  )

  return (
    <div className="space-y-6">
      {/* Basic Settings */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Bot className="w-6 h-6 text-blue-600" />
          <h3 className="text-xl font-semibold text-gray-900">Basic Settings</h3>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Approved Domain *
            </label>
            <input
              type="url"
              value={formData.approvedDomain}
              onChange={(e) => setFormData(prev => ({ ...prev, approvedDomain: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="https://yourwebsite.com"
            />
            <p className="text-sm text-gray-500 mt-1">
              The domain where this chatbot widget can be embedded
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              System Prompt
            </label>
            <textarea
              value={formData.systemPrompt}
              onChange={(e) => setFormData(prev => ({ ...prev, systemPrompt: e.target.value }))}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="You are a helpful assistant for..."
            />
            <p className="text-sm text-gray-500 mt-1">
              Instructions that define how your chatbot should behave
            </p>
          </div>
        </div>
      </div>

      {/* Knowledge Base Assignment */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-6">
          <Database className="w-6 h-6 text-green-600" />
          <h3 className="text-xl font-semibold text-gray-900">Knowledge Base</h3>
        </div>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Assigned Knowledge Base
            </label>
            <select
              value={formData.assignedKbId}
              onChange={(e) => setFormData(prev => ({ ...prev, assignedKbId: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">No knowledge base assigned</option>
              {availableKBs.map((kb) => (
                <option key={kb.id} value={kb.id}>
                  {kb.name} ({kb.kbType})
                </option>
              ))}
            </select>
            <p className="text-sm text-gray-500 mt-1">
              Select a knowledge base to provide context for your chatbot's responses
            </p>
          </div>

          {formData.assignedKbId && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <p className="text-sm text-green-800">
                <strong>Selected:</strong> {availableKBs.find(kb => kb.id === formData.assignedKbId)?.name}
                <br />
                <strong>Type:</strong> {availableKBs.find(kb => kb.id === formData.assignedKbId)?.kbType}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Widget Testing */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <ExternalLink className="w-6 h-6 text-purple-600" />
          <h3 className="text-xl font-semibold text-gray-900">Test Your Chatbot</h3>
        </div>

        <div className="flex items-center space-x-4">
          <a
            href={`/widget/${chatbot.id}`}
            target="_blank"
            rel="noopener noreferrer"
            className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
          >
            <ExternalLink className="w-4 h-4 mr-2" />
            Open Chat Widget
          </a>
          <p className="text-sm text-gray-600">
            Test your chatbot in a new window to see how it will appear to your visitors
          </p>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-between items-center">
        <button
          onClick={() => setShowDeleteConfirm(true)}
          disabled={isLoading}
          className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
        >
          <Trash2 className="w-4 h-4 mr-2" />
          Delete Chatbot
        </button>

        <button
          onClick={handleSave}
          disabled={isLoading}
          className="inline-flex items-center px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
        >
          <Save className="w-4 h-4 mr-2" />
          {isLoading ? 'Saving...' : 'Save Changes'}
        </button>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="w-6 h-6 text-red-600" />
              <h3 className="text-lg font-semibold text-gray-900">Delete Chatbot</h3>
            </div>
            
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete this chatbot? This action cannot be undone. 
              All chat history ({chatbot._count.chatSessions} conversations) will be permanently deleted.
            </p>
            
            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                disabled={isLoading}
                className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                onClick={handleDelete}
                disabled={isLoading}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors disabled:opacity-50"
              >
                {isLoading ? 'Deleting...' : 'Delete Permanently'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
