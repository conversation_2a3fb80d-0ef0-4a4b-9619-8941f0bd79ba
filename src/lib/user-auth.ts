import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

export async function verifyUser() {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new Error("Not authenticated");
  }
  return { user: session.user };
}

export async function verifyUserWithSubscription() {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new Error("Not authenticated");
  }
  
  // Additional subscription check can be added here if needed
  return { user: session.user };
}
