import { headers } from 'next/headers'
import { notFound } from 'next/navigation'
import { PrismaClient } from '@prisma/client'
import ChatClient from '@/components/ChatClient'

const prisma = new PrismaClient()

interface WidgetPageProps {
  params: {
    botId: string
  }
}

export default async function WidgetPage({ params }: WidgetPageProps) {
  const { botId } = await params

  // Get the referer header to check domain
  const headersList = await headers()
  const referer = headersList.get('referer')
  
  // Fetch the chatbot with its configuration
  const chatbot = await prisma.chatbot.findUnique({
    where: { id: botId },
    select: {
      id: true,
      approvedDomain: true,
      widgetConfig: true,
      user: {
        select: {
          subscription: {
            select: {
              status: true,
              plan: {
                select: {
                  features: true
                }
              }
            }
          }
        }
      }
    }
  })

  // Check if chatbot exists
  if (!chatbot) {
    notFound()
  }

  // Check if user's subscription is active
  if (!chatbot.user.subscription || chatbot.user.subscription.status !== 'active') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-100">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md text-center">
          <h1 className="text-xl font-semibold text-gray-800 mb-4">
            Chat Unavailable
          </h1>
          <p className="text-gray-600">
            This chatbot is currently unavailable due to subscription issues.
          </p>
        </div>
      </div>
    )
  }

  // Domain validation - check if referer starts with approved domain
  if (referer && !referer.startsWith(chatbot.approvedDomain)) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-red-50">
        <div className="bg-white p-8 rounded-lg shadow-lg max-w-md text-center border border-red-200">
          <h1 className="text-xl font-semibold text-red-800 mb-4">
            Access Denied
          </h1>
          <p className="text-red-600 mb-4">
            This chatbot is not authorized for this domain.
          </p>
          <p className="text-sm text-gray-500">
            Authorized domain: {chatbot.approvedDomain}
          </p>
        </div>
      </div>
    )
  }

  // Default widget configuration if none exists
  const defaultWidgetConfig = {
    primaryColor: '#3B82F6',
    welcomeMessage: 'Hello! How can I help you today?',
    placeholder: 'Type your message...',
    position: 'bottom-right',
    theme: 'light'
  }

  const widgetConfig = chatbot.widgetConfig || defaultWidgetConfig

  return (
    <div className="min-h-screen">
      <ChatClient 
        botId={botId} 
        widgetConfig={widgetConfig}
      />
    </div>
  )
}
