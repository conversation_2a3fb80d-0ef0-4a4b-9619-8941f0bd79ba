import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient()

// Request validation schema
const processKbSchema = z.object({
  chatbotId: z.string()
})

export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json()
    const { chatbotId } = processKbSchema.parse(body)

    console.log(`Processing KB for chatbot: ${chatbotId}`)

    // Fetch all structured data for the chatbot
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: {
        structuredKbBrand: true,
        structuredKbContact: true,
        structuredKbTeachers: true,
        structuredKbTtcs: true,
        structuredKbRetreats: true,
        structuredKbPolicies: true,
        structuredKbFaqs: true
      }
    })

    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 })
    }

    // Create human-readable text chunks from structured data
    const chunks: Array<{ content: string; source: string }> = []

    // Process School Brand
    if (chatbot.structuredKbBrand) {
      const brand = chatbot.structuredKbBrand
      let content = `School Information: ${brand.schoolName}`
      if (brand.tagline) content += `\nTagline: ${brand.tagline}`
      if (brand.schoolType) content += `\nType: ${brand.schoolType}`
      if (brand.yogaStylesTaught.length > 0) content += `\nYoga Styles: ${brand.yogaStylesTaught.join(', ')}`
      if (brand.missionStatement) content += `\nMission: ${brand.missionStatement}`
      if (brand.aboutTheSchool) content += `\nAbout: ${brand.aboutTheSchool}`
      if (brand.founderInfo) content += `\nFounder: ${brand.founderInfo}`
      
      chunks.push({ content, source: "School Brand" })
    }

    // Process School Contact
    if (chatbot.structuredKbContact) {
      const contact = chatbot.structuredKbContact
      let content = "Contact Information"
      if (contact.fullAddress) content += `\nAddress: ${contact.fullAddress}`
      if (contact.primaryPhone) content += `\nPhone: ${contact.primaryPhone}`
      if (contact.whatsappNumber) content += `\nWhatsApp: ${contact.whatsappNumber}`
      if (contact.primaryEmail) content += `\nEmail: ${contact.primaryEmail}`
      if (contact.websiteUrl) content += `\nWebsite: ${contact.websiteUrl}`
      if (contact.howToReach) content += `\nHow to Reach: ${contact.howToReach}`
      if (contact.googleMapsLink) content += `\nLocation: ${contact.googleMapsLink}`
      
      chunks.push({ content, source: "Contact Information" })
    }

    // Process Teachers
    chatbot.structuredKbTeachers.forEach((teacher) => {
      let content = `Teacher: ${teacher.name}`
      if (teacher.role) content += `\nRole: ${teacher.role}`
      if (teacher.bio) content += `\nBio: ${teacher.bio}`
      if (teacher.certifications.length > 0) content += `\nCertifications: ${teacher.certifications.join(', ')}`
      
      chunks.push({ content, source: `Teacher: ${teacher.name}` })
    })

    // Process TTCs
    chatbot.structuredKbTtcs.forEach((ttc) => {
      let content = `Teacher Training Course: ${ttc.name}`
      if (ttc.certificationBody) content += `\nCertification Body: ${ttc.certificationBody}`
      if (ttc.summary) content += `\nSummary: ${ttc.summary}`
      if (ttc.duration) content += `\nDuration: ${ttc.duration}`
      if (ttc.skillLevel) content += `\nSkill Level: ${ttc.skillLevel}`
      if (ttc.curriculumDetails) content += `\nCurriculum: ${ttc.curriculumDetails}`
      if (ttc.sampleDailySchedule) content += `\nDaily Schedule: ${ttc.sampleDailySchedule}`
      if (ttc.inclusions.length > 0) content += `\nInclusions: ${ttc.inclusions.join(', ')}`
      if (ttc.exclusions.length > 0) content += `\nExclusions: ${ttc.exclusions.join(', ')}`
      if (ttc.applicationProcess) content += `\nApplication Process: ${ttc.applicationProcess}`
      
      // Add pricing information
      if (Array.isArray(ttc.priceOptions) && ttc.priceOptions.length > 0) {
        content += `\nPricing: ${ttc.priceOptions.map((p: any) => `${p.type}: $${p.price}`).join(', ')}`
      }
      
      // Add upcoming dates
      if (Array.isArray(ttc.upcomingDates) && ttc.upcomingDates.length > 0) {
        content += `\nUpcoming Dates: ${ttc.upcomingDates.map((d: any) => `${d.start} to ${d.end} (${d.status})`).join(', ')}`
      }
      
      chunks.push({ content, source: `TTC: ${ttc.name}` })
    })

    // Process Retreats
    chatbot.structuredKbRetreats.forEach((retreat) => {
      let content = `Retreat: ${retreat.name}`
      if (retreat.theme) content += `\nTheme: ${retreat.theme}`
      if (retreat.duration) content += `\nDuration: ${retreat.duration}`
      if (retreat.intendedAudience) content += `\nIntended Audience: ${retreat.intendedAudience}`
      if (retreat.highlights.length > 0) content += `\nHighlights: ${retreat.highlights.join(', ')}`
      
      // Add pricing information
      if (Array.isArray(retreat.priceOptions) && retreat.priceOptions.length > 0) {
        content += `\nPricing: ${retreat.priceOptions.map((p: any) => `${p.type}: $${p.price}`).join(', ')}`
      }
      
      // Add upcoming dates
      if (Array.isArray(retreat.upcomingDates) && retreat.upcomingDates.length > 0) {
        content += `\nUpcoming Dates: ${retreat.upcomingDates.map((d: any) => `${d.start} to ${d.end} (${d.status})`).join(', ')}`
      }
      
      chunks.push({ content, source: `Retreat: ${retreat.name}` })
    })

    // Process Policies
    if (chatbot.structuredKbPolicies) {
      const policies = chatbot.structuredKbPolicies
      if (policies.codeOfConduct) {
        chunks.push({ content: `Code of Conduct: ${policies.codeOfConduct}`, source: "Policies" })
      }
      if (policies.paymentPolicy) {
        chunks.push({ content: `Payment Policy: ${policies.paymentPolicy}`, source: "Policies" })
      }
      if (policies.cancellationAndRefundPolicy) {
        chunks.push({ content: `Cancellation and Refund Policy: ${policies.cancellationAndRefundPolicy}`, source: "Policies" })
      }
    }

    // Process FAQs
    chatbot.structuredKbFaqs.forEach((faq) => {
      const content = `FAQ: ${faq.question}\nAnswer: ${faq.answer}`
      chunks.push({ content, source: "FAQ" })
    })

    console.log(`Generated ${chunks.length} knowledge chunks`)

    // Delete existing chunks and create new ones
    await prisma.$transaction(async (tx) => {
      // Delete existing chunks
      await tx.knowledgeBaseChunk.deleteMany({
        where: { chatbotId }
      })

      // Create new chunks
      if (chunks.length > 0) {
        await tx.knowledgeBaseChunk.createMany({
          data: chunks.map(chunk => ({
            chatbotId,
            content: chunk.content,
            source: chunk.source
          }))
        })

        // Update tsvector column for Full-Text Search
        await tx.$executeRaw`
          UPDATE "KnowledgeBaseChunk"
          SET content_tsvector = to_tsvector('english', content)
          WHERE "chatbotId" = ${chatbotId}
        `
      }
    })

    console.log(`Successfully processed KB for chatbot: ${chatbotId}`)

    return NextResponse.json({
      success: true,
      message: "Knowledge base processed successfully",
      chunksCreated: chunks.length
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors }, 
        { status: 400 }
      )
    }
    
    console.error("KB processing error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
