import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { PrismaClient } from "@prisma/client"
import { GoogleGenerativeAI } from "@google/generative-ai"
import { v4 as uuidv4 } from 'uuid'

const prisma = new PrismaClient()

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '')

// Request validation schema
const chatRequestSchema = z.object({
  botId: z.string(),
  visitorId: z.string(),
  message: z.string().min(1).max(1000)
})

export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json()
    const { botId, visitorId, message } = chatRequestSchema.parse(body)

    // Fetch chatbot with all necessary relations
    const chatbot = await prisma.chatbot.findUnique({
      where: { id: botId },
      include: {
        user: {
          include: {
            subscription: {
              include: {
                plan: true
              }
            }
          }
        },
        personas: {
          where: { isActive: true },
          take: 1
        }
      }
    })

    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 })
    }

    // Check subscription status
    if (!chatbot.user.subscription || chatbot.user.subscription.status !== 'active') {
      return NextResponse.json({ error: "Subscription inactive" }, { status: 403 })
    }

    // Check token limits
    const subscription = chatbot.user.subscription
    const tokenLimit = subscription.plan.features.tokenLimit || 10000
    if (subscription.tokensUsedThisPeriod >= tokenLimit) {
      return NextResponse.json({ error: "Token limit exceeded" }, { status: 429 })
    }

    // Get or create visitor
    let visitor = await prisma.visitor.findUnique({
      where: { id: visitorId }
    })

    if (!visitor) {
      visitor = await prisma.visitor.create({
        data: { id: visitorId }
      })
    }

    // Get or create chat session
    let chatSession = await prisma.chatSession.findFirst({
      where: {
        chatbotId: botId,
        visitorId: visitorId
      },
      include: {
        messages: {
          orderBy: { createdAt: 'desc' },
          take: 10 // Last 10 messages for context
        }
      }
    })

    if (!chatSession) {
      chatSession = await prisma.chatSession.create({
        data: {
          chatbotId: botId,
          visitorId: visitorId,
          ablyChannel: `chat-${uuidv4()}`
        },
        include: {
          messages: true
        }
      })
    }

    // Save visitor message
    await prisma.message.create({
      data: {
        chatSessionId: chatSession.id,
        senderType: 'VISITOR',
        content: message
      }
    })

    // Determine effective KB type
    const effectiveKbType = chatbot.kbTypeOverride || chatbot.user.subscription.plan.features.kbType || 'simple'

    // Retrieve context based on KB strategy
    let context = ''
    
    if (effectiveKbType === 'structured') {
      // Use Full-Text Search for structured KB
      const searchQuery = message.trim().split(/\s+/).join(' & ')
      
      try {
        const results = await prisma.$queryRaw<Array<{ content: string, rank: number }>>`
          SELECT content, ts_rank(content_tsvector, to_tsquery('english', ${searchQuery})) as rank
          FROM "KnowledgeBaseChunk"
          WHERE "chatbotId" = ${botId}
          AND to_tsquery('english', ${searchQuery}) @@ content_tsvector
          ORDER BY rank DESC
          LIMIT 5;
        `
        context = results.map(r => r.content).join('\n---\n')
      } catch (error) {
        console.error('FTS query error:', error)
        // Fallback to simple text search if FTS fails
        context = chatbot.simpleKbText || ''
      }
    } else {
      // Use simple KB text directly
      context = chatbot.simpleKbText || ''
    }

    // Build conversation history
    const conversationHistory = chatSession.messages
      .reverse()
      .map(msg => `${msg.senderType === 'VISITOR' ? 'User' : 'Assistant'}: ${msg.content}`)
      .join('\n')

    // Get active persona
    const persona = chatbot.personas[0]
    const personaText = persona ? persona.personaText : 'You are a helpful yoga assistant.'

    // Assemble the prompt
    const systemPrompt = chatbot.systemPrompt || 'You are a helpful yoga school assistant.'
    
    const fullPrompt = `${systemPrompt}

PERSONA:
${personaText}

KNOWLEDGE BASE:
${context}

CONVERSATION HISTORY:
${conversationHistory}

Current User Message: ${message}

Please respond helpfully based on the knowledge base and conversation context. Keep responses concise and friendly.`

    // Call Gemini API
    const model = genAI.getGenerativeModel({ model: chatbot.llmModel || 'gemini-pro' })
    
    const result = await model.generateContent(fullPrompt)
    const response = result.response
    const responseText = response.text()

    // Estimate token usage (rough approximation)
    const estimatedTokens = Math.ceil((fullPrompt.length + responseText.length) / 4)

    // Save bot response
    await prisma.message.create({
      data: {
        chatSessionId: chatSession.id,
        senderType: 'LLM',
        content: responseText
      }
    })

    // Update token usage
    await prisma.subscription.update({
      where: { id: subscription.id },
      data: {
        tokensUsedThisPeriod: {
          increment: estimatedTokens
        }
      }
    })

    // Update session token count
    await prisma.chatSession.update({
      where: { id: chatSession.id },
      data: {
        tokenCount: {
          increment: estimatedTokens
        }
      }
    })

    return NextResponse.json({
      response: responseText,
      tokensUsed: estimatedTokens
    })

  } catch (error) {
    console.error('Chat API error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
