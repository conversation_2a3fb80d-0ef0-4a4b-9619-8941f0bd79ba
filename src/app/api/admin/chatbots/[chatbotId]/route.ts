import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { PrismaClient } from "@prisma/client"
import { verifyAdmin } from "@/lib/admin-auth"

const prisma = new PrismaClient()

// Schema for chatbot settings override
const chatbotUpdateSchema = z.object({
  systemPrompt: z.string().optional(),
  llmProvider: z.string().optional(),
  llmModel: z.string().optional(),
  encryptedLlmApiKey: z.string().optional(),
  kbTypeOverride: z.enum(["simple", "structured"]).optional(),
  simpleKbCharacterLimit: z.number().int().min(1000).optional(),
})

// GET /api/admin/chatbots/[chatbotId] - Get specific chatbot details
export async function GET(
  request: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    await verifyAdmin()

    const { chatbotId } = params

    const chatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            subscription: {
              include: {
                plan: true
              }
            }
          }
        },
        personas: true,
        _count: {
          select: {
            chatSessions: true,
            knowledgeChunks: true
          }
        }
      }
    })

    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 })
    }

    return NextResponse.json(chatbot)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    
    console.error("Error fetching chatbot:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// PUT /api/admin/chatbots/[chatbotId] - Update chatbot settings (admin overrides)
export async function PUT(
  request: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    await verifyAdmin()

    const { chatbotId } = params
    const body = await request.json()
    const validatedData = chatbotUpdateSchema.parse(body)

    // Check if chatbot exists
    const existingChatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId }
    })

    if (!existingChatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 })
    }

    // Update chatbot with admin overrides
    const updatedChatbot = await prisma.chatbot.update({
      where: { id: chatbotId },
      data: validatedData,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            subscription: {
              include: {
                plan: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json(updatedChatbot)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors }, 
        { status: 400 }
      )
    }
    
    console.error("Error updating chatbot:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// DELETE /api/admin/chatbots/[chatbotId] - Delete chatbot (admin action)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    await verifyAdmin()

    const { chatbotId } = params

    // Check if chatbot exists
    const existingChatbot = await prisma.chatbot.findUnique({
      where: { id: chatbotId },
      include: {
        _count: {
          select: {
            chatSessions: true
          }
        }
      }
    })

    if (!existingChatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 })
    }

    // Delete chatbot (this will cascade delete related data)
    await prisma.chatbot.delete({
      where: { id: chatbotId }
    })

    return NextResponse.json({ 
      message: "Chatbot deleted successfully",
      deletedSessions: existingChatbot._count.chatSessions
    })
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    
    console.error("Error deleting chatbot:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
