import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { PrismaClient } from "@prisma/client"
import { verifyAdmin } from "@/lib/admin-auth"

const prisma = new PrismaClient()

// Schema for plan validation (same as in main route)
const planUpdateSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  price: z.number().int().min(0, "Price must be a positive integer").optional(),
  features: z.object({
    chatbotLimit: z.number().int().min(1, "Chatbot limit must be at least 1"),
    tokenLimit: z.number().int().min(1000, "Token limit must be at least 1000"),
    kbType: z.enum(["simple", "structured"], {
      errorMap: () => ({ message: "KB type must be 'simple' or 'structured'" })
    }),
    canUseBYOK: z.boolean(),
  }).and(z.record(z.any())).optional(), // Allow additional properties
  isActive: z.boolean().optional(),
})

// PUT /api/admin/plans/[planId] - Update a plan
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ planId: string }> }
) {
  try {
    await verifyAdmin()

    const { planId } = await params
    const body = await request.json()
    const validatedData = planUpdateSchema.parse(body)

    // Check if plan exists
    const existingPlan = await prisma.plan.findUnique({
      where: { id: planId }
    })

    if (!existingPlan) {
      return NextResponse.json({ error: "Plan not found" }, { status: 404 })
    }

    // If name is being updated, check for conflicts
    if (validatedData.name && validatedData.name !== existingPlan.name) {
      const nameConflict = await prisma.plan.findUnique({
        where: { name: validatedData.name }
      })

      if (nameConflict) {
        return NextResponse.json(
          { error: "A plan with this name already exists" }, 
          { status: 400 }
        )
      }
    }

    const updatedPlan = await prisma.plan.update({
      where: { id: planId },
      data: validatedData
    })

    return NextResponse.json(updatedPlan)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors }, 
        { status: 400 }
      )
    }
    
    console.error("Error updating plan:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// DELETE /api/admin/plans/[planId] - Delete a plan
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ planId: string }> }
) {
  try {
    await verifyAdmin()

    const { planId } = await params

    // Check if plan exists
    const existingPlan = await prisma.plan.findUnique({
      where: { id: planId },
      include: {
        _count: {
          select: { subscriptions: true }
        }
      }
    })

    if (!existingPlan) {
      return NextResponse.json({ error: "Plan not found" }, { status: 404 })
    }

    // Check if plan has active subscriptions
    if (existingPlan._count.subscriptions > 0) {
      return NextResponse.json(
        { error: "Cannot delete plan with active subscriptions" }, 
        { status: 400 }
      )
    }

    await prisma.plan.delete({
      where: { id: planId }
    })

    return NextResponse.json({ message: "Plan deleted successfully" })
  } catch (error) {
    if (error instanceof Error && error.message === "Not authorized") {
      return NextResponse.json({ error: "Not authorized" }, { status: 403 })
    }
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    
    console.error("Error deleting plan:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
