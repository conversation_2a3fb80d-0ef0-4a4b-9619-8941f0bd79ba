import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { PrismaClient } from "@prisma/client"
import { verifyUser } from "@/lib/user-auth"

const prisma = new PrismaClient()

// Schema for KB assignment
const assignKbSchema = z.object({
  knowledgeBaseId: z.string().nullable()
})

// POST /api/user/chatbots/[chatbotId]/assign-kb - Assign or unassign KB to chatbot
export async function POST(
  request: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const { user } = await verifyUser()
    const { chatbotId } = await params
    const body = await request.json()
    const { knowledgeBaseId } = assignKbSchema.parse(body)

    // Check if chatbot exists and belongs to user
    const chatbot = await prisma.chatbot.findFirst({
      where: { 
        id: chatbotId,
        userId: user.id
      },
      include: {
        assignedKb: true
      }
    })

    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 })
    }

    // If assigning a KB, validate it exists and belongs to user
    if (knowledgeBaseId) {
      const knowledgeBase = await prisma.knowledgeBase.findFirst({
        where: {
          id: knowledgeBaseId,
          userId: user.id
        },
        include: {
          assignedChatbot: true
        }
      })

      if (!knowledgeBase) {
        return NextResponse.json({ error: "Knowledge base not found" }, { status: 404 })
      }

      // Check if KB is already assigned to another chatbot
      if (knowledgeBase.assignedChatbot && knowledgeBase.assignedChatbot.id !== chatbotId) {
        return NextResponse.json({ 
          error: `This knowledge base is already assigned to chatbot: ${knowledgeBase.assignedChatbot.approvedDomain}` 
        }, { status: 400 })
      }
    }

    // Update assignment in a transaction
    const updatedChatbot = await prisma.$transaction(async (tx) => {
      // If chatbot had a previous KB, clear that assignment
      if (chatbot.assignedKb) {
        // This will be handled by the unique constraint, but we can be explicit
      }

      // If assigning a new KB and it was assigned to another chatbot, clear that
      if (knowledgeBaseId) {
        await tx.chatbot.updateMany({
          where: { assignedKbId: knowledgeBaseId },
          data: { assignedKbId: null }
        })
      }

      // Update the chatbot
      return await tx.chatbot.update({
        where: { id: chatbotId },
        data: { assignedKbId: knowledgeBaseId },
        include: {
          assignedKb: {
            select: {
              id: true,
              name: true,
              kbType: true
            }
          }
        }
      })
    })

    return NextResponse.json({
      success: true,
      message: knowledgeBaseId 
        ? "Knowledge base assigned successfully" 
        : "Knowledge base unassigned successfully",
      chatbot: updatedChatbot
    })
  } catch (error) {
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors }, 
        { status: 400 }
      )
    }
    
    console.error("Error assigning knowledge base:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
