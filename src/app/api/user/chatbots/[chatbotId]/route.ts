import { NextRequest, NextResponse } from "next/server"
import { z } from "zod"
import { PrismaClient } from "@prisma/client"
import { verifyUser } from "@/lib/user-auth"

const prisma = new PrismaClient()

// Schema for updating chatbot
const updateChatbotSchema = z.object({
  approvedDomain: z.string().url("Must be a valid URL").optional(),
  systemPrompt: z.string().optional(),
  widgetConfig: z.object({
    primaryColor: z.string().optional(),
    welcomeMessage: z.string().optional(),
    placeholder: z.string().optional(),
    position: z.string().optional(),
    theme: z.string().optional()
  }).optional()
})

// GET /api/user/chatbots/[chatbotId] - Get specific chatbot
export async function GET(
  request: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const { user } = await verifyUser()
    const { chatbotId } = params

    const chatbot = await prisma.chatbot.findFirst({
      where: { 
        id: chatbotId,
        userId: user.id // Ensure user owns this chatbot
      },
      include: {
        assignedKb: {
          select: {
            id: true,
            name: true,
            kbType: true
          }
        },
        user: {
          select: {
            subscription: {
              include: {
                plan: true
              }
            }
          }
        },
        personas: {
          where: { isActive: true }
        },
        _count: {
          select: {
            chatSessions: true,
            knowledgeChunks: true
          }
        }
      }
    })

    if (!chatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 })
    }

    return NextResponse.json(chatbot)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    
    console.error("Error fetching chatbot:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// PUT /api/user/chatbots/[chatbotId] - Update chatbot
export async function PUT(
  request: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const { user } = await verifyUser()
    const { chatbotId } = params
    const body = await request.json()
    const validatedData = updateChatbotSchema.parse(body)

    // Check if chatbot exists and belongs to user
    const existingChatbot = await prisma.chatbot.findFirst({
      where: { 
        id: chatbotId,
        userId: user.id
      }
    })

    if (!existingChatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 })
    }

    // Update chatbot
    const updatedChatbot = await prisma.chatbot.update({
      where: { id: chatbotId },
      data: validatedData,
      include: {
        assignedKb: {
          select: {
            id: true,
            name: true,
            kbType: true
          }
        },
        user: {
          select: {
            subscription: {
              include: {
                plan: true
              }
            }
          }
        },
        personas: {
          where: { isActive: true }
        },
        _count: {
          select: {
            chatSessions: true,
            knowledgeChunks: true
          }
        }
      }
    })

    return NextResponse.json(updatedChatbot)
  } catch (error) {
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors }, 
        { status: 400 }
      )
    }
    
    console.error("Error updating chatbot:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

// DELETE /api/user/chatbots/[chatbotId] - Delete chatbot
export async function DELETE(
  request: NextRequest,
  { params }: { params: { chatbotId: string } }
) {
  try {
    const { user } = await verifyUser()
    const { chatbotId } = params

    // Check if chatbot exists and belongs to user
    const existingChatbot = await prisma.chatbot.findFirst({
      where: { 
        id: chatbotId,
        userId: user.id
      },
      include: {
        _count: {
          select: {
            chatSessions: true
          }
        }
      }
    })

    if (!existingChatbot) {
      return NextResponse.json({ error: "Chatbot not found" }, { status: 404 })
    }

    // Delete chatbot (this will cascade delete related data)
    await prisma.chatbot.delete({
      where: { id: chatbotId }
    })

    return NextResponse.json({ 
      message: "Chatbot deleted successfully",
      deletedSessions: existingChatbot._count.chatSessions
    })
  } catch (error) {
    if (error instanceof Error && error.message === "Not authenticated") {
      return NextResponse.json({ error: "Not authenticated" }, { status: 401 })
    }
    
    console.error("Error deleting chatbot:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
