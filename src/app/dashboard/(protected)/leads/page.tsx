import { BarChart3, TrendingUp, Users, Mail, Settings, ArrowRight } from "lucide-react"

export default function LeadsPage() {
  const leadStats = [
    { label: "Total Leads", value: "127", change: "+12%", positive: true },
    { label: "This Week", value: "23", change: "+8%", positive: true },
    { label: "Conversion Rate", value: "18%", change: "+3%", positive: true },
    { label: "Avg Response Time", value: "4.2h", change: "-15%", positive: true }
  ]

  const leadSources = [
    { name: "Chatbot Conversations", count: "89", percentage: "70%" },
    { name: "Contact Form", count: "23", percentage: "18%" },
    { name: "Direct Inquiries", count: "15", percentage: "12%" }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-purple-600 via-violet-600 to-indigo-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center space-x-3 mb-4">
            <BarChart3 className="h-8 w-8 text-purple-300" />
            <h1 className="text-4xl font-bold">Analytics & Leads</h1>
          </div>
          <p className="text-purple-100 text-lg">
            View and manage leads generated from your chatbot
          </p>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
      </div>

      {/* Lead Stats */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Lead Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {leadStats.map((stat, index) => (
            <div key={index} className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
              <div className="flex items-center justify-between mb-2">
                <p className="text-slate-600 text-sm font-medium">{stat.label}</p>
                <TrendingUp className={`h-4 w-4 ${stat.positive ? 'text-green-500' : 'text-red-500'}`} />
              </div>
              <p className="text-3xl font-bold text-slate-800 mb-1">{stat.value}</p>
              <p className={`text-sm font-medium ${stat.positive ? 'text-green-600' : 'text-red-600'}`}>
                {stat.change} from last week
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Lead Sources */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-slate-800 mb-4">Lead Sources</h3>
          <div className="space-y-4">
            {leadSources.map((source, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                <div>
                  <p className="font-medium text-slate-800">{source.name}</p>
                  <p className="text-sm text-slate-600">{source.count} leads</p>
                </div>
                <span className="text-lg font-bold text-purple-600">{source.percentage}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-slate-800 mb-4">Recent Activity</h3>
          <div className="space-y-4">
            <div className="flex items-center space-x-3 p-3 bg-slate-50 rounded-lg">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <Mail className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-800">New lead from chatbot</p>
                <p className="text-xs text-slate-600">2 minutes ago</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 bg-slate-50 rounded-lg">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Users className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-800">Lead converted to customer</p>
                <p className="text-xs text-slate-600">1 hour ago</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Coming Soon Notice */}
      <div className="bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 rounded-2xl p-6">
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <Settings className="h-6 w-6 text-amber-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-amber-900 mb-2">
              Coming Soon
            </h3>
            <p className="text-amber-800 text-sm">
              Lead management will be implemented in Part 6 of the development plan. You&apos;ll be able to view detailed lead information, track conversions, export data, and manage follow-ups.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
