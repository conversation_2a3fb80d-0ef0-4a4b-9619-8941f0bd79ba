import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { redirect } from "next/navigation"
import { PrismaClient } from "@prisma/client"
import { Database, Bot, Plus } from "lucide-react"
import KnowledgeBaseClient from "@/components/KnowledgeBaseClient"

const prisma = new PrismaClient()

export default async function KnowledgeBasePage() {
  // Get user session
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    redirect('/login')
  }

  // Fetch user's chatbots with subscription info (temporary - using old approach)
  const chatbots = await prisma.chatbot.findMany({
    where: { userId: session.user.id },
    include: {
      user: {
        select: {
          subscription: {
            include: {
              plan: true
            }
          }
        }
      },
      _count: {
        select: {
          knowledgeChunks: true
        }
      }
    },
    orderBy: { id: 'desc' }
  })

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-cyan-600 to-teal-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center space-x-3 mb-4">
            <Database className="h-8 w-8 text-cyan-300" />
            <h1 className="text-4xl font-bold">Knowledge Base</h1>
          </div>
          <p className="text-blue-100 text-lg">
            Configure your chatbot&apos;s knowledge about your business
          </p>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
      </div>

      {/* Knowledge Base Client Component */}
      <KnowledgeBaseClient chatbots={chatbots} />
    </div>
  )
}
