import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { redirect, notFound } from "next/navigation"
import { PrismaClient } from "@prisma/client"
import { Database, ArrowLeft } from "lucide-react"
import Link from "next/link"
import KnowledgeBaseSettingsClient from "@/components/KnowledgeBaseSettingsClient"

const prisma = new PrismaClient()

interface KBSettingsPageProps {
  params: {
    id: string
  }
}

export default async function KBSettingsPage({ params }: KBSettingsPageProps) {
  // Get user session
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    redirect('/login')
  }

  const { id } = await params

  // Fetch the specific knowledge base with all related data
  const knowledgeBase = await prisma.knowledgeBase.findFirst({
    where: { 
      id,
      userId: session.user.id // Ensure user owns this KB
    },
    include: {
      assignedChatbot: {
        select: {
          id: true,
          approvedDomain: true
        }
      },
      _count: {
        select: {
          knowledgeChunks: true
        }
      }
    }
  })

  if (!knowledgeBase) {
    notFound()
  }

  // Fetch user's chatbots for assignment dropdown
  const chatbots = await prisma.chatbot.findMany({
    where: { userId: session.user.id },
    select: {
      id: true,
      approvedDomain: true,
      assignedKbId: true
    },
    orderBy: { approvedDomain: 'asc' }
  })

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-green-600 via-teal-600 to-cyan-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center space-x-3 mb-4">
            <Link
              href="/dashboard/kb"
              className="inline-flex items-center text-green-200 hover:text-white transition-colors"
            >
              <ArrowLeft className="h-5 w-5 mr-2" />
              Back to Knowledge Bases
            </Link>
          </div>
          <div className="flex items-center space-x-3 mb-4">
            <Database className="h-8 w-8 text-green-300" />
            <h1 className="text-4xl font-bold">Knowledge Base Settings</h1>
          </div>
          <p className="text-green-100 text-lg">
            Manage: {knowledgeBase.name}
          </p>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
      </div>

      {/* KB Settings Client Component */}
      <KnowledgeBaseSettingsClient 
        knowledgeBase={knowledgeBase} 
        chatbots={chatbots}
      />
    </div>
  )
}
