import { getServerSession } from "next-auth/next"
import { authOptions } from "@/lib/auth"
import { redirect } from "next/navigation"
import { PrismaClient } from "@prisma/client"
import { Bot, Plus } from "lucide-react"
import ChatbotsClient from "@/components/ChatbotsClient"

const prisma = new PrismaClient()

export default async function ChatbotsPage() {
  // Get user session
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    redirect('/login')
  }

  // Fetch user's chatbots with subscription info
  const user = await prisma.user.findUnique({
    where: { id: session.user.id },
    include: {
      subscription: {
        include: {
          plan: true
        }
      },
      chatbots: {
        include: {
          assignedKb: {
            select: {
              id: true,
              name: true,
              kbType: true
            }
          },
          _count: {
            select: {
              chatSessions: true
            }
          }
        },
        orderBy: { id: 'desc' }
      }
    }
  })

  if (!user) {
    redirect('/login')
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center space-x-3 mb-4">
            <Bot className="h-8 w-8 text-blue-300" />
            <h1 className="text-4xl font-bold">Chatbots</h1>
          </div>
          <p className="text-blue-100 text-lg">
            Manage your chatbots and their configurations
          </p>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
      </div>

      {/* Chatbots Client Component */}
      <ChatbotsClient user={user} />
    </div>
  )
}
