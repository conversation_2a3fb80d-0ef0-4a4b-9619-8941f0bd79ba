import { getServerSession } from "next-auth/next"
import { redirect } from "next/navigation"
import { Sidebar } from "@/components/Sidebar"
import { Header } from "@/components/header"
import { authOptions } from "@/lib/auth"

export default async function ProtectedLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const session = await getServerSession(authOptions)

  if (!session) {
    redirect("/login")
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Sidebar */}
      <Sidebar user={session.user} />

      {/* Main content */}
      <div className="ml-72 flex flex-col min-h-screen">
        <Header user={session.user} />
        <main className="flex-1 p-8">
          <div className="max-w-7xl mx-auto">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}
