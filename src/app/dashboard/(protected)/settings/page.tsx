import { Settings, Pa<PERSON>, <PERSON><PERSON>, <PERSON>, Shield, Bell, ArrowRight } from "lucide-react"

export default function SettingsPage() {
  const settingsSections = [
    {
      title: "Chatbot Appearance",
      description: "Customize colors, logo, and widget design",
      icon: Palette,
      gradient: "from-pink-500 to-rose-600",
      status: "Not configured"
    },
    {
      title: "Bot Behavior",
      description: "Configure responses and conversation flow",
      icon: Bot,
      gradient: "from-blue-500 to-indigo-600",
      status: "Not configured"
    },
    {
      title: "Email Integration",
      description: "Set up SMTP and email notifications",
      icon: Mail,
      gradient: "from-green-500 to-emerald-600",
      status: "Not configured"
    },
    {
      title: "Security Settings",
      description: "Manage API keys and access controls",
      icon: Shield,
      gradient: "from-red-500 to-pink-600",
      status: "Not configured"
    },
    {
      title: "Notifications",
      description: "Configure alerts and notification preferences",
      icon: Bell,
      gradient: "from-purple-500 to-violet-600",
      status: "Not configured"
    }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-slate-600 via-gray-600 to-zinc-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center space-x-3 mb-4">
            <Settings className="h-8 w-8 text-slate-300" />
            <h1 className="text-4xl font-bold">Settings</h1>
          </div>
          <p className="text-slate-100 text-lg">
            Configure your chatbot appearance and behavior
          </p>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
      </div>

      {/* Settings Sections */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Configuration Options</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {settingsSections.map((section) => (
            <div
              key={section.title}
              className="group relative overflow-hidden bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
            >
              <div className="p-6">
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r ${section.gradient} text-white mb-4`}>
                  <section.icon className="h-6 w-6" />
                </div>
                <h3 className="text-lg font-semibold text-slate-800 mb-2">
                  {section.title}
                </h3>
                <p className="text-slate-600 text-sm mb-4">
                  {section.description}
                </p>
                <div className="flex items-center justify-between">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                    {section.status}
                  </span>
                  <ArrowRight className="h-4 w-4 text-slate-400 group-hover:text-slate-600 group-hover:translate-x-1 transition-all duration-200" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Settings Preview */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="bg-gradient-to-r from-slate-50 to-gray-50 px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-slate-800">Settings Preview</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-medium text-slate-800">Current Configuration</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-slate-600">Theme:</span>
                  <span className="text-slate-800">Default</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-600">Language:</span>
                  <span className="text-slate-800">English</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-600">Response Time:</span>
                  <span className="text-slate-800">Instant</span>
                </div>
              </div>
            </div>
            <div className="space-y-4">
              <h4 className="font-medium text-slate-800">Widget Preview</h4>
              <div className="bg-slate-50 rounded-lg p-4 border-2 border-dashed border-slate-300">
                <div className="text-center text-slate-500 text-sm">
                  Chatbot widget preview will appear here
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Coming Soon Notice */}
      <div className="bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 rounded-2xl p-6">
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <Settings className="h-6 w-6 text-amber-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-amber-900 mb-2">
              Coming Soon
            </h3>
            <p className="text-amber-800 text-sm">
              Settings management will be implemented in Part 7 of the development plan. You&apos;ll be able to customize your chatbot&apos;s appearance, configure behavior, set up email integration, and manage security settings.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
