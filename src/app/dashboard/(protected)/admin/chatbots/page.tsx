"use client"

import { useState, useEffect } from "react"
import { Bo<PERSON>, Settings, Trash2, MessageSquare, Database, User, Globe } from "lucide-react"

interface Chatbot {
  id: string
  approvedDomain: string
  systemPrompt: string | null
  llmProvider: string
  llmModel: string
  encryptedLlmApiKey: string | null
  kbTypeOverride: string | null
  simpleKbCharacterLimit: number | null
  createdAt: string
  user: {
    id: string
    email: string
    name: string | null
    subscription: {
      plan: {
        name: string
        features: {
          kbType: string
        }
      }
    } | null
  }
  _count: {
    chatSessions: number
    knowledgeChunks: number
  }
}

export default function AdminChatbotsPage() {
  const [chatbots, setChatbots] = useState<Chatbot[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [selectedChatbot, setSelectedChatbot] = useState<Chatbot | null>(null)
  const [showSettingsModal, setShowSettingsModal] = useState(false)

  useEffect(() => {
    fetchChatbots()
  }, [])

  const fetchChatbots = async () => {
    try {
      const response = await fetch("/api/admin/chatbots")
      if (!response.ok) {
        throw new Error("Failed to fetch chatbots")
      }
      const data = await response.json()
      setChatbots(data)
    } catch (error) {
      setError("Failed to load chatbots")
      console.error("Error fetching chatbots:", error)
    } finally {
      setLoading(false)
    }
  }

  const deleteChatbot = async (chatbotId: string) => {
    if (!confirm("Are you sure you want to delete this chatbot? This will delete all associated data including chat sessions and knowledge base.")) {
      return
    }

    try {
      const response = await fetch(`/api/admin/chatbots/${chatbotId}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to delete chatbot")
      }

      await fetchChatbots() // Refresh the list
    } catch (error) {
      alert(error instanceof Error ? error.message : "Failed to delete chatbot")
    }
  }

  const openSettingsModal = (chatbot: Chatbot) => {
    setSelectedChatbot(chatbot)
    setShowSettingsModal(true)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getKbType = (chatbot: Chatbot) => {
    return chatbot.kbTypeOverride || chatbot.user.subscription?.plan.features.kbType || 'simple'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading chatbots...</div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-purple-600 via-violet-600 to-indigo-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-3 mb-4">
                <Bot className="h-8 w-8 text-purple-300" />
                <h1 className="text-4xl font-bold">Chatbot Management</h1>
              </div>
              <p className="text-purple-100 text-lg">
                Override chatbot settings and monitor usage across all users
              </p>
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold">{chatbots.length}</div>
              <div className="text-purple-200 text-sm">Total Chatbots</div>
            </div>
          </div>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
        <div className="absolute bottom-0 left-0 -mb-8 -ml-8 w-24 h-24 bg-white/5 rounded-full"></div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Total Chatbots</p>
              <p className="text-3xl font-bold text-slate-800">{chatbots.length}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-violet-600 rounded-xl flex items-center justify-center">
              <Bot className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Total Sessions</p>
              <p className="text-3xl font-bold text-slate-800">{chatbots.reduce((acc, c) => acc + c._count.chatSessions, 0)}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
              <MessageSquare className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Knowledge Chunks</p>
              <p className="text-3xl font-bold text-slate-800">{chatbots.reduce((acc, c) => acc + c._count.knowledgeChunks, 0)}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-xl flex items-center justify-center">
              <Database className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-slate-600 text-sm font-medium">Admin Overrides</p>
              <p className="text-3xl font-bold text-slate-800">{chatbots.filter(c => c.kbTypeOverride).length}</p>
            </div>
            <div className="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-600 rounded-xl flex items-center justify-center">
              <Settings className="h-6 w-6 text-white" />
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 rounded-2xl p-4">
          <div className="text-red-800">{error}</div>
        </div>
      )}

      {/* Chatbots List */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">All Chatbots</h2>
        <div className="bg-white shadow-lg rounded-2xl overflow-hidden border border-gray-100">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gradient-to-r from-slate-50 to-gray-50">
              <tr>
                <th className="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">
                  Chatbot
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">
                  Owner
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">
                  Configuration
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">
                  Usage
                </th>
                <th className="px-6 py-4 text-left text-xs font-semibold text-slate-600 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-100">
              {chatbots.length === 0 ? (
                <tr>
                  <td colSpan={5} className="text-center py-12">
                    <div className="flex flex-col items-center">
                      <Bot className="h-12 w-12 text-slate-400 mb-4" />
                      <div className="text-slate-500 text-lg font-medium">No chatbots found</div>
                      <div className="text-slate-400 text-sm">Chatbots will appear here once users create them</div>
                    </div>
                  </td>
                </tr>
              ) : (
                chatbots.map((chatbot) => (
                  <tr key={chatbot.id} className="hover:bg-slate-50 transition-colors">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-violet-600 rounded-xl flex items-center justify-center">
                          <Bot className="h-5 w-5 text-white" />
                        </div>
                        <div>
                          <div className="text-sm font-semibold text-slate-800">
                            {chatbot.approvedDomain}
                          </div>
                          <div className="text-sm text-slate-500">
                            {chatbot.id.slice(0, 8)}...
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {chatbot.user.name || 'No name'}
                        </div>
                        <div className="text-sm text-gray-500">{chatbot.user.email}</div>
                        {chatbot.user.subscription && (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                            {chatbot.user.subscription.plan.name}
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">
                        <div className="flex items-center mb-1">
                          <Settings className="h-3 w-3 mr-1" />
                          {chatbot.llmProvider} / {chatbot.llmModel}
                        </div>
                        <div className="flex items-center mb-1">
                          <Database className="h-3 w-3 mr-1" />
                          KB: {getKbType(chatbot)}
                        </div>
                        {chatbot.kbTypeOverride && (
                          <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                            Admin Override
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        <div className="flex items-center mb-1">
                          <MessageSquare className="h-3 w-3 mr-1" />
                          {chatbot._count.chatSessions} sessions
                        </div>
                        <div className="flex items-center">
                          <Database className="h-3 w-3 mr-1" />
                          {chatbot._count.knowledgeChunks} chunks
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => openSettingsModal(chatbot)}
                          className="text-indigo-600 hover:text-indigo-900"
                        >
                          <Settings className="h-4 w-4" />
                        </button>
                        <button
                          onClick={() => deleteChatbot(chatbot.id)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
        </table>
        </div>
      </div>

      {showSettingsModal && selectedChatbot && (
        <ChatbotSettingsModal
          chatbot={selectedChatbot}
          onClose={() => {
            setShowSettingsModal(false)
            setSelectedChatbot(null)
          }}
          onSuccess={() => {
            setShowSettingsModal(false)
            setSelectedChatbot(null)
            fetchChatbots()
          }}
        />
      )}
    </div>
  )
}

// Modal component for chatbot settings override
function ChatbotSettingsModal({
  chatbot,
  onClose,
  onSuccess
}: {
  chatbot: Chatbot
  onClose: () => void
  onSuccess: () => void
}) {
  const [formData, setFormData] = useState({
    systemPrompt: chatbot.systemPrompt || "",
    llmProvider: chatbot.llmProvider,
    llmModel: chatbot.llmModel,
    kbTypeOverride: chatbot.kbTypeOverride || "",
    simpleKbCharacterLimit: chatbot.simpleKbCharacterLimit?.toString() || "",
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")

    try {
      const updateData: any = {
        systemPrompt: formData.systemPrompt || null,
        llmProvider: formData.llmProvider,
        llmModel: formData.llmModel,
      }

      if (formData.kbTypeOverride) {
        updateData.kbTypeOverride = formData.kbTypeOverride
      }

      if (formData.simpleKbCharacterLimit) {
        updateData.simpleKbCharacterLimit = parseInt(formData.simpleKbCharacterLimit)
      }

      const response = await fetch(`/api/admin/chatbots/${chatbot.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(updateData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to update chatbot")
      }

      onSuccess()
    } catch (error) {
      setError(error instanceof Error ? error.message : "Failed to update chatbot")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full p-6 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-medium text-gray-900">
            Admin Settings Override - {chatbot.approvedDomain}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>

        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <p className="text-sm text-blue-800">
            <strong>Owner:</strong> {chatbot.user.name || chatbot.user.email}
            {chatbot.user.subscription && (
              <span className="ml-2 px-2 py-1 text-xs bg-blue-100 rounded">
                {chatbot.user.subscription.plan.name}
              </span>
            )}
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700">System Prompt Override</label>
            <textarea
              rows={4}
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
              value={formData.systemPrompt}
              onChange={(e) => setFormData({ ...formData, systemPrompt: e.target.value })}
              placeholder="Leave empty to use default system prompt"
            />
            <p className="text-xs text-gray-500 mt-1">
              Override the default system prompt for this chatbot
            </p>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">LLM Provider</label>
              <select
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                value={formData.llmProvider}
                onChange={(e) => setFormData({ ...formData, llmProvider: e.target.value })}
              >
                <option value="gemini">Gemini</option>
                <option value="openai">OpenAI</option>
                <option value="openrouter">OpenRouter</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700">LLM Model</label>
              <input
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                value={formData.llmModel}
                onChange={(e) => setFormData({ ...formData, llmModel: e.target.value })}
                placeholder="e.g., gemini-pro, gpt-4"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700">Knowledge Base Type Override</label>
            <select
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
              value={formData.kbTypeOverride}
              onChange={(e) => setFormData({ ...formData, kbTypeOverride: e.target.value })}
            >
              <option value="">Use plan default ({chatbot.user.subscription?.plan.features.kbType || 'simple'})</option>
              <option value="simple">Simple</option>
              <option value="structured">Structured</option>
            </select>
            <p className="text-xs text-gray-500 mt-1">
              Override the knowledge base type allowed by the user's plan
            </p>
          </div>

          {(formData.kbTypeOverride === "simple" || (!formData.kbTypeOverride && chatbot.user.subscription?.plan.features.kbType === "simple")) && (
            <div>
              <label className="block text-sm font-medium text-gray-700">Simple KB Character Limit</label>
              <input
                type="number"
                min="1000"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
                value={formData.simpleKbCharacterLimit}
                onChange={(e) => setFormData({ ...formData, simpleKbCharacterLimit: e.target.value })}
                placeholder="e.g., 50000"
              />
              <p className="text-xs text-gray-500 mt-1">
                Override the character limit for simple knowledge base
              </p>
            </div>
          )}

          {error && (
            <div className="text-red-600 text-sm">{error}</div>
          )}

          <div className="flex justify-end space-x-3 pt-4 border-t">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-md disabled:opacity-50"
            >
              {loading ? "Updating..." : "Update Settings"}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
