"use client"

import { useState, useEffect } from "react"
import { Plus, Edit, Trash2, Users, Check, X, DollarSign, Bot, Database, Key } from "lucide-react"

interface Plan {
  id: string
  name: string
  price: number
  features: {
    chatbotLimit: number
    tokenLimit: number
    kbType: string
    canUseBYOK: boolean
  }
  isActive: boolean
  _count: {
    subscriptions: number
  }
}

export default function AdminPlansPage() {
  const [plans, setPlans] = useState<Plan[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState("")
  const [showCreateForm, setShowCreateForm] = useState(false)
  const [editingPlan, setEditingPlan] = useState<Plan | null>(null)

  useEffect(() => {
    fetchPlans()
  }, [])

  const fetchPlans = async () => {
    try {
      const response = await fetch("/api/admin/plans")
      if (!response.ok) {
        throw new Error("Failed to fetch plans")
      }
      const data = await response.json()
      setPlans(data)
    } catch (error) {
      setError("Failed to load plans")
      console.error("Error fetching plans:", error)
    } finally {
      setLoading(false)
    }
  }

  const deletePlan = async (planId: string) => {
    if (!confirm("Are you sure you want to delete this plan?")) {
      return
    }

    try {
      const response = await fetch(`/api/admin/plans/${planId}`, {
        method: "DELETE",
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || "Failed to delete plan")
      }

      await fetchPlans() // Refresh the list
    } catch (error) {
      alert(error instanceof Error ? error.message : "Failed to delete plan")
    }
  }

  const handleEdit = (plan: Plan) => {
    setEditingPlan(plan)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading plans...</div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Subscription Plans</h1>
          <p className="text-gray-600 mt-2">
            Create and manage subscription plans for your yoga school platform
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="inline-flex items-center px-6 py-3 border border-transparent text-sm font-medium rounded-lg text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 shadow-lg hover:shadow-xl transition-all duration-200"
        >
          <Plus className="h-5 w-5 mr-2" />
          Create New Plan
        </button>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 rounded-lg">
          <div className="flex">
            <X className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"></div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {plans.map((plan) => (
            <div key={plan.id} className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-shadow duration-300 border border-gray-100 overflow-hidden">
              {/* Plan Header */}
              <div className="bg-gradient-to-r from-indigo-500 to-purple-600 px-6 py-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-bold text-white">{plan.name}</h3>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    plan.isActive
                      ? "bg-green-100 text-green-800"
                      : "bg-red-100 text-red-800"
                  }`}>
                    {plan.isActive ? (
                      <>
                        <Check className="h-3 w-3 mr-1" />
                        Active
                      </>
                    ) : (
                      <>
                        <X className="h-3 w-3 mr-1" />
                        Inactive
                      </>
                    )}
                  </span>
                </div>
                <div className="mt-2 flex items-baseline">
                  <span className="text-3xl font-bold text-white">₹{(plan.price / 100).toFixed(0)}</span>
                  <span className="text-indigo-100 ml-1">/month</span>
                </div>
              </div>

              {/* Plan Features */}
              <div className="px-6 py-4 space-y-3">
                <div className="flex items-center text-gray-700">
                  <Bot className="h-5 w-5 text-indigo-500 mr-3" />
                  <span className="text-sm">
                    <span className="font-semibold">{plan.features.chatbotLimit}</span> Chatbot{plan.features.chatbotLimit > 1 ? 's' : ''}
                  </span>
                </div>

                <div className="flex items-center text-gray-700">
                  <DollarSign className="h-5 w-5 text-green-500 mr-3" />
                  <span className="text-sm">
                    <span className="font-semibold">{plan.features.tokenLimit.toLocaleString()}</span> Tokens/month
                  </span>
                </div>

                <div className="flex items-center text-gray-700">
                  <Database className="h-5 w-5 text-blue-500 mr-3" />
                  <span className="text-sm">
                    <span className="font-semibold capitalize">{plan.features.kbType}</span> Knowledge Base
                  </span>
                </div>

                <div className="flex items-center text-gray-700">
                  <Key className="h-5 w-5 text-purple-500 mr-3" />
                  <span className="text-sm">
                    BYOK: <span className="font-semibold">{plan.features.canUseBYOK ? "Enabled" : "Disabled"}</span>
                  </span>
                </div>
              </div>

              {/* Subscribers */}
              <div className="px-6 py-3 bg-gray-50 border-t border-gray-100">
                <div className="flex items-center justify-between">
                  <div className="flex items-center text-gray-600">
                    <Users className="h-4 w-4 mr-2" />
                    <span className="text-sm">{plan._count.subscriptions} subscriber{plan._count.subscriptions !== 1 ? 's' : ''}</span>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleEdit(plan)}
                      className="p-2 text-indigo-600 hover:text-indigo-800 hover:bg-indigo-50 rounded-lg transition-colors duration-200"
                      title="Edit plan"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => deletePlan(plan.id)}
                      className={`p-2 rounded-lg transition-colors duration-200 ${
                        plan._count.subscriptions > 0
                          ? "text-gray-400 cursor-not-allowed"
                          : "text-red-600 hover:text-red-800 hover:bg-red-50"
                      }`}
                      disabled={plan._count.subscriptions > 0}
                      title={plan._count.subscriptions > 0 ? "Cannot delete plan with active subscriptions" : "Delete plan"}
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {plans.length === 0 && !loading && (
        <div className="text-center py-12">
          <div className="mx-auto h-24 w-24 text-gray-400">
            <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900">No plans found</h3>
          <p className="mt-2 text-gray-500">Get started by creating your first subscription plan.</p>
          <button
            onClick={() => setShowCreateForm(true)}
            className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Plan
          </button>
        </div>
      )}

      {/* Modals */}
      {showCreateForm && (
        <PlanModal
          onClose={() => setShowCreateForm(false)}
          onSuccess={() => {
            setShowCreateForm(false)
            fetchPlans()
          }}
        />
      )}

      {editingPlan && (
        <PlanModal
          plan={editingPlan}
          onClose={() => setEditingPlan(null)}
          onSuccess={() => {
            setEditingPlan(null)
            fetchPlans()
          }}
        />
      )}
    </div>
  )
}

// Unified modal component for creating and editing plans
function PlanModal({
  plan,
  onClose,
  onSuccess
}: {
  plan?: Plan | null
  onClose: () => void
  onSuccess: () => void
}) {
  const isEditing = !!plan

  const [formData, setFormData] = useState({
    name: plan?.name || "",
    price: plan ? (plan.price / 100).toString() : "",
    chatbotLimit: plan?.features.chatbotLimit.toString() || "1",
    tokenLimit: plan?.features.tokenLimit.toString() || "50000",
    kbType: plan?.features.kbType || "simple",
    canUseBYOK: plan?.features.canUseBYOK || false,
    isActive: plan?.isActive ?? true,
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")

    try {
      const url = isEditing ? `/api/admin/plans/${plan.id}` : "/api/admin/plans"
      const method = isEditing ? "PUT" : "POST"

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: formData.name,
          price: parseInt(formData.price) * 100, // Convert to cents
          features: {
            chatbotLimit: parseInt(formData.chatbotLimit),
            tokenLimit: parseInt(formData.tokenLimit),
            kbType: formData.kbType,
            canUseBYOK: formData.canUseBYOK,
          },
          isActive: formData.isActive,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `Failed to ${isEditing ? 'update' : 'create'} plan`)
      }

      onSuccess()
    } catch (error) {
      setError(error instanceof Error ? error.message : `Failed to ${isEditing ? 'update' : 'create'} plan`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-xl max-w-lg w-full p-6 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-gray-900">
            {isEditing ? 'Edit Plan' : 'Create New Plan'}
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-5">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Plan Name</label>
              <input
                type="text"
                required
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="e.g., Basic, Pro, Enterprise"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Price (₹)</label>
              <input
                type="number"
                required
                min="0"
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                value={formData.price}
                onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                placeholder="999"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Chatbot Limit</label>
              <input
                type="number"
                required
                min="1"
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                value={formData.chatbotLimit}
                onChange={(e) => setFormData({ ...formData, chatbotLimit: e.target.value })}
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Token Limit</label>
              <input
                type="number"
                required
                min="1000"
                step="1000"
                className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                value={formData.tokenLimit}
                onChange={(e) => setFormData({ ...formData, tokenLimit: e.target.value })}
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Knowledge Base Type</label>
            <select
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              value={formData.kbType}
              onChange={(e) => setFormData({ ...formData, kbType: e.target.value })}
            >
              <option value="simple">Simple</option>
              <option value="structured">Structured</option>
            </select>
          </div>

          <div className="space-y-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="canUseBYOK"
                className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                checked={formData.canUseBYOK}
                onChange={(e) => setFormData({ ...formData, canUseBYOK: e.target.checked })}
              />
              <label htmlFor="canUseBYOK" className="ml-3 block text-sm text-gray-900">
                Allow Bring Your Own Key (BYOK)
              </label>
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="isActive"
                className="h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                checked={formData.isActive}
                onChange={(e) => setFormData({ ...formData, isActive: e.target.checked })}
              />
              <label htmlFor="isActive" className="ml-3 block text-sm text-gray-900">
                Plan is active
              </label>
            </div>
          </div>

          {error && (
            <div className="bg-red-50 border-l-4 border-red-400 p-3 rounded">
              <div className="flex">
                <X className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <p className="text-sm text-red-800">{error}</p>
                </div>
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 text-sm font-medium text-white bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 rounded-lg disabled:opacity-50 transition-all duration-200"
            >
              {loading ? (
                <div className="flex items-center">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {isEditing ? 'Updating...' : 'Creating...'}
                </div>
              ) : (
                isEditing ? 'Update Plan' : 'Create Plan'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
