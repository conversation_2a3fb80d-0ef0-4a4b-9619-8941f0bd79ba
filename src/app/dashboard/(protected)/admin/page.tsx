import { getServerSession } from "next-auth/next"
import { redirect } from "next/navigation"
import { authOptions } from "@/lib/auth"
import { Crown, Users, Bot, Settings, TrendingUp, Shield, Database, ArrowRight } from "lucide-react"

export default async function AdminDashboardPage() {
  const session = await getServerSession(authOptions)

  if (!session || session.user.role !== "ADMIN") {
    redirect("/dashboard")
  }

  const adminFeatures = [
    {
      title: "Subscription Plans",
      description: "Create and manage subscription plans and pricing",
      icon: Crown,
      href: "/dashboard/admin/plans",
      gradient: "from-yellow-500 to-orange-600",
      bgGradient: "from-yellow-50 to-orange-50",
      stats: "3 active plans",
      status: "Ready"
    },
    {
      title: "User Management",
      description: "Onboard new users and manage existing subscriptions",
      icon: Users,
      href: "/dashboard/admin/users",
      gradient: "from-green-500 to-emerald-600",
      bgGradient: "from-green-50 to-emerald-50",
      stats: "24 total users",
      status: "Coming Soon"
    },
    {
      title: "Chatbot Management",
      description: "Override chatbot settings and monitor usage",
      icon: Bot,
      href: "/dashboard/admin/chatbots",
      gradient: "from-purple-500 to-violet-600",
      bgGradient: "from-purple-50 to-violet-50",
      stats: "18 chatbots",
      status: "Coming Soon"
    }
  ]

  const systemStats = [
    { label: "Total Revenue", value: "₹1,24,500", change: "+12%", positive: true },
    { label: "Active Users", value: "24", change: "+8%", positive: true },
    { label: "Chatbots Deployed", value: "18", change: "+15%", positive: true },
    { label: "Support Tickets", value: "3", change: "-25%", positive: true }
  ]

  return (
    <div className="space-y-8">
      {/* Admin Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-red-600 via-pink-600 to-purple-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center space-x-3 mb-4">
            <Crown className="h-8 w-8 text-yellow-300" />
            <h1 className="text-4xl font-bold">Admin Dashboard</h1>
          </div>
          <p className="text-red-100 text-lg mb-6">
            Complete control over the YogaBot Live platform
          </p>
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <Shield className="h-5 w-5 text-green-300" />
              <span className="text-sm">System Secure</span>
            </div>
            <div className="flex items-center space-x-2">
              <Database className="h-5 w-5 text-blue-300" />
              <span className="text-sm">Database Healthy</span>
            </div>
          </div>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
        <div className="absolute bottom-0 left-0 -mb-8 -ml-8 w-24 h-24 bg-white/5 rounded-full"></div>
      </div>

      {/* System Stats */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">System Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {systemStats.map((stat, index) => (
            <div key={index} className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
              <div className="flex items-center justify-between mb-2">
                <p className="text-slate-600 text-sm font-medium">{stat.label}</p>
                <TrendingUp className={`h-4 w-4 ${stat.positive ? 'text-green-500' : 'text-red-500'}`} />
              </div>
              <p className="text-3xl font-bold text-slate-800 mb-1">{stat.value}</p>
              <p className={`text-sm font-medium ${stat.positive ? 'text-green-600' : 'text-red-600'}`}>
                {stat.change} from last month
              </p>
            </div>
          ))}
        </div>
      </div>

      {/* Admin Features */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Admin Features</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {adminFeatures.map((feature) => (
            <a
              key={feature.title}
              href={feature.href}
              className={`group relative overflow-hidden bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 ${
                feature.status === 'Coming Soon' ? 'opacity-75 cursor-not-allowed' : ''
              }`}
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${feature.bgGradient} opacity-50`}></div>
              <div className="relative p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r ${feature.gradient} text-white`}>
                    <feature.icon className="h-6 w-6" />
                  </div>
                  <div className="flex flex-col items-end">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      feature.status === 'Ready'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {feature.status}
                    </span>
                    {feature.status === 'Ready' && (
                      <ArrowRight className="h-5 w-5 text-slate-400 group-hover:text-slate-600 group-hover:translate-x-1 transition-all duration-200 mt-2" />
                    )}
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-slate-800 mb-2 group-hover:text-slate-900">
                  {feature.title}
                </h3>
                <p className="text-slate-600 text-sm mb-3">
                  {feature.description}
                </p>
                <span className="text-xs text-slate-500">{feature.stats}</span>
              </div>
            </a>
          ))}
        </div>
      </div>

      {/* Development Status */}
      <div className="bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 rounded-2xl p-6">
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <Settings className="h-6 w-6 text-amber-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-amber-900 mb-2">
              Development Status
            </h3>
            <p className="text-amber-800 text-sm mb-4">
              This is Part 1 of the development plan. User management and chatbot management features will be implemented in the next development phases.
            </p>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm text-amber-800">Plans Management - Complete</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span className="text-sm text-amber-800">User & Chatbot Management - In Progress</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
