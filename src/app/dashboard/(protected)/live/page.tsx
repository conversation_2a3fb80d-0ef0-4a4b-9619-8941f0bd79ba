import { MessageSquare, <PERSON>, Clock, Settings, ArrowRight } from "lucide-react"

export default function LiveChatPage() {
  const chatStats = [
    { label: "Active Chats", value: "2", color: "text-green-600" },
    { label: "Waiting", value: "0", color: "text-yellow-600" },
    { label: "Today's Chats", value: "12", color: "text-blue-600" },
    { label: "Avg Response", value: "2.3s", color: "text-purple-600" }
  ]

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="relative overflow-hidden bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <div className="flex items-center space-x-3 mb-4">
            <MessageSquare className="h-8 w-8 text-green-300" />
            <h1 className="text-4xl font-bold">Live Chat</h1>
          </div>
          <p className="text-green-100 text-lg">
            Monitor active conversations and take over when needed
          </p>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
      </div>

      {/* Chat Stats */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Chat Overview</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {chatStats.map((stat, index) => (
            <div key={index} className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
              <p className="text-slate-600 text-sm font-medium mb-2">{stat.label}</p>
              <p className={`text-3xl font-bold ${stat.color}`}>{stat.value}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Chat Interface Preview */}
      <div className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden">
        <div className="bg-gradient-to-r from-slate-50 to-gray-50 px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-slate-800">Live Chat Interface</h3>
        </div>
        <div className="p-6">
          <div className="flex items-center justify-center h-64 bg-slate-50 rounded-xl border-2 border-dashed border-slate-300">
            <div className="text-center">
              <MessageSquare className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-600 mb-2">Chat Interface Coming Soon</h3>
              <p className="text-slate-500 text-sm">Real-time chat monitoring and takeover functionality</p>
            </div>
          </div>
        </div>
      </div>

      {/* Coming Soon Notice */}
      <div className="bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 rounded-2xl p-6">
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            <Settings className="h-6 w-6 text-amber-600" />
          </div>
          <div>
            <h3 className="text-lg font-semibold text-amber-900 mb-2">
              Coming Soon
            </h3>
            <p className="text-amber-800 text-sm">
              Live chat management will be implemented in Part 3 of the development plan. You&apos;ll be able to monitor active conversations, take over from the AI when needed, and manage chat settings.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
