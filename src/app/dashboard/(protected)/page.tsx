import { getServerSession } from "next-auth/next"
import { Database, MessageSquare, Settings, BarChart3, Crown, Bot, Users, Zap, ArrowRight } from "lucide-react"

export default async function DashboardPage() {
  const session = await getServerSession()

  const quickActions = [
    {
      title: "Knowledge Base",
      description: "Configure your chatbot's knowledge about your yoga school",
      icon: Database,
      href: "/dashboard/kb",
      gradient: "from-blue-500 to-cyan-600",
      bgGradient: "from-blue-50 to-cyan-50",
      stats: "3 sections configured"
    },
    {
      title: "Live Chat",
      description: "Monitor active conversations and take over when needed",
      icon: MessageSquare,
      href: "/dashboard/live",
      gradient: "from-green-500 to-emerald-600",
      bgGradient: "from-green-50 to-emerald-50",
      stats: "2 active chats"
    },
    {
      title: "Analytics",
      description: "View leads and conversation analytics",
      icon: BarChart3,
      href: "/dashboard/leads",
      gradient: "from-purple-500 to-violet-600",
      bgGradient: "from-purple-50 to-violet-50",
      stats: "12 leads this week"
    },
    {
      title: "Settings",
      description: "Configure your chatbot appearance and behavior",
      icon: Settings,
      href: "/dashboard/settings",
      gradient: "from-slate-500 to-gray-600",
      bgGradient: "from-slate-50 to-gray-50",
      stats: "Last updated 2 days ago"
    }
  ]

  const adminActions = [
    {
      title: "Subscription Plans",
      description: "Manage pricing and plan features",
      icon: Crown,
      href: "/dashboard/admin/plans",
      stats: "3 active plans"
    },
    {
      title: "User Management",
      description: "Onboard users and manage subscriptions",
      icon: Users,
      href: "/dashboard/admin/users",
      stats: "24 active users"
    },
    {
      title: "Chatbot Management",
      description: "Override settings and monitor usage",
      icon: Bot,
      href: "/dashboard/admin/chatbots",
      stats: "18 chatbots deployed"
    }
  ]

  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="relative overflow-hidden bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 rounded-2xl p-8 text-white">
        <div className="relative z-10">
          <h1 className="text-4xl font-bold mb-2">
            Welcome back, {session?.user?.name || session?.user?.email?.split('@')[0]}! 👋
          </h1>
          <p className="text-indigo-100 text-lg">
            Manage your AI-powered chatbot platform for yoga schools
          </p>
          <div className="mt-6 flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <Zap className="h-5 w-5 text-yellow-300" />
              <span className="text-sm">System Status: All Good</span>
            </div>
            <div className="flex items-center space-x-2">
              <Bot className="h-5 w-5 text-green-300" />
              <span className="text-sm">3 Active Chatbots</span>
            </div>
          </div>
        </div>
        <div className="absolute top-0 right-0 -mt-4 -mr-4 w-32 h-32 bg-white/10 rounded-full"></div>
        <div className="absolute bottom-0 left-0 -mb-8 -ml-8 w-24 h-24 bg-white/5 rounded-full"></div>
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-2xl font-bold text-slate-800 mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {quickActions.map((action) => (
            <a
              key={action.title}
              href={action.href}
              className="group relative overflow-hidden bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100"
            >
              <div className={`absolute inset-0 bg-gradient-to-br ${action.bgGradient} opacity-50`}></div>
              <div className="relative p-6">
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r ${action.gradient} text-white mb-4`}>
                  <action.icon className="h-6 w-6" />
                </div>
                <h3 className="text-lg font-semibold text-slate-800 mb-2 group-hover:text-slate-900">
                  {action.title}
                </h3>
                <p className="text-slate-600 text-sm mb-3 line-clamp-2">
                  {action.description}
                </p>
                <div className="flex items-center justify-between">
                  <span className="text-xs text-slate-500">{action.stats}</span>
                  <ArrowRight className="h-4 w-4 text-slate-400 group-hover:text-slate-600 group-hover:translate-x-1 transition-all duration-200" />
                </div>
              </div>
            </a>
          ))}
        </div>
      </div>

      {/* Admin Panel */}
      {session?.user?.role === "ADMIN" && (
        <div>
          <div className="flex items-center space-x-3 mb-6">
            <Crown className="h-6 w-6 text-red-500" />
            <h2 className="text-2xl font-bold text-slate-800">Admin Panel</h2>
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
              Administrator Access
            </span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {adminActions.map((action) => (
              <a
                key={action.title}
                href={action.href}
                className="group relative overflow-hidden bg-gradient-to-br from-red-50 to-pink-50 rounded-2xl p-6 border border-red-100 hover:border-red-200 transition-all duration-300 hover:shadow-lg"
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r from-red-500 to-pink-600 text-white">
                    <action.icon className="h-6 w-6" />
                  </div>
                  <ArrowRight className="h-5 w-5 text-red-400 group-hover:text-red-600 group-hover:translate-x-1 transition-all duration-200" />
                </div>
                <h3 className="text-lg font-semibold text-slate-800 mb-2">
                  {action.title}
                </h3>
                <p className="text-slate-600 text-sm mb-3">
                  {action.description}
                </p>
                <span className="text-xs text-red-600 font-medium">{action.stats}</span>
              </a>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
