## **The Grand Blueprint: Building YogaBot Live (app.yogabot.live)**

### **Guiding Principles**

1.  **One Feature at a Time:** Focus solely on the current feature. Do not look ahead. Complete it, test it, commit it.
2.  **Commit Often:** After every single feature, commit your code with a clear message (e.g., `feat: Implement admin plan management UI`). This is your safety net.
3.  **Non-Destructive Development:** This plan is structured to be additive. We build the foundation first, then add floors. We won't be tearing down walls.
4.  **Backend First, Then Frontend:** For most features, we will build the API endpoint and its logic first. This allows you to build the user interface against a working, testable backend.

---

### **Part 0: The Foundational Database Schema**
Description: This is the complete and final Prisma schema for the entire application. It is the single source of truth for the database structure. This schema will be implemented in Feature 0.2.

// This is your Prisma schema file.

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_PRISMA_URL")
}

// ===================================
// 1. Core SaaS & User Models
// ===================================

model User {
  id           String        @id @default(cuid())
  email        String        @unique
  name         String?
  password     String        // Stores a secure hash from bcrypt
  role         Role          @default(USER)
  chatbots     Chatbot[]     // A user can now have multiple chatbots
  subscription Subscription?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
}

enum Role {
  ADMIN
  USER
}

model Plan {
  id        String   @id @default(cuid())
  name      String   @unique // e.g., "Basic", "Pro", "Enterprise"
  price     Int      // Price in cents
  features  Json     // Defines capabilities: { chatbotLimit: 1, tokenLimit: 50000, kbType: "simple", canUseBYOK: false, ... }
  isActive  Boolean  @default(true)

  subscriptions Subscription[]
}

model Subscription {
  id                     String    @id @default(cuid())
  userId                 String    @unique
  user                   User      @relation(fields: [userId], references: [id])
  planId                 String
  plan                   Plan      @relation(fields: [planId], references: [id])
  status                 String    // e.g., "active", "cancelled", "past_due"
  currentPeriodEnd       DateTime
  razorpaySubscriptionId String?   @unique

  // Usage Tracking
  tokensUsedThisPeriod   Int       @default(0)
  sessionsThisPeriod     Int       @default(0)
}

// ===================================
// 2. Chatbot & Prompt Configuration
// ===================================

model Chatbot {
  id                    String    @id @default(cuid())
  userId                String
  user                  User      @relation(fields: [userId], references: [id])
  approvedDomain        String

  // Admin-controlled LLM settings & Overrides
  systemPrompt          String?   @db.Text
  llmProvider           String    @default("gemini")
  llmModel              String    @default("gemini-pro")
  encryptedLlmApiKey    String?   // For BYOK (Bring Your Own Key)
  kbTypeOverride        String?   // Admin override: "simple" or "structured"
  simpleKbCharacterLimit Int?     // Admin override for character limit on simple KB

  // User-controlled settings
  widgetConfig          Json?
  smtpConfig            Json?     // Encrypted SMTP credentials
  personas              Persona[]

  // Knowledge Base Data
  simpleKbText          String?   @db.Text // For "simple" plan users
  structuredKbBrand     SchoolBrand?
  structuredKbContact   SchoolContact?
  structuredKbTeachers  Teacher[]
  structuredKbTtcs      TTC[]
  structuredKbRetreats  Retreat[]
  structuredKbPolicies  Policy[]
  structuredKbFaqs      FAQ[]

  // Searchable Data & History
  knowledgeChunks       KnowledgeBaseChunk[]
  chatSessions          ChatSession[]
}

model Persona {
  id          String   @id @default(cuid())
  chatbotId   String
  chatbot     Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  name        String
  personaText String   @db.Text
  isActive    Boolean  @default(true)
}

// This model holds the final, searchable data generated from THE STRUCTURED KB ONLY.
model KnowledgeBaseChunk {
  id                 String   @id @default(cuid())
  chatbotId          String
  chatbot            Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  content            String   @db.Text
  embedding          vector(384)? // Optional, not used for FTS query-time retrieval
  content_tsvector   Unsupported("tsvector")? // Special column for Full-Text Search

  source             String?  // e.g., "TTC: 200-Hour Foundational" to identify the source
  
  @@index([content_tsvector], map: "content_tsvector_idx", type: Gin) // CRITICAL for FTS performance
}

// ===================================
// 3. Structured Knowledge Base Models
// (Unchanged from previous versions)
// ===================================

model SchoolBrand {
  id              String   @id @default(cuid())
  chatbotId       String   @unique
  chatbot         Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  schoolName      String
  tagline         String?
  schoolType      String?
  yogaStylesTaught String[]
  missionStatement String?   @db.Text
  aboutTheSchool  String?   @db.Text
  founderInfo     String?   @db.Text
}

model SchoolContact {
  id              String   @id @default(cuid())
  chatbotId       String   @unique
  chatbot         Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  fullAddress     String?
  googleMapsLink  String?
  howToReach      String?   @db.Text
  primaryPhone    String?
  whatsappNumber  String?
  primaryEmail    String?
  websiteUrl      String?
  socialMediaLinks Json?     // [{ platform: 'Instagram', url: '...' }]
}

model Teacher {
  id              String   @id @default(cuid())
  chatbotId       String
  chatbot         Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  name            String
  role            String?
  photoUrl        String?
  bio             String?   @db.Text
  certifications  String[]
}

model TTC {
  id                  String   @id @default(cuid())
  chatbotId           String
  chatbot             Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  name                String
  certificationBody   String?
  summary             String?   @db.Text
  duration            String?
  skillLevel          String?
  curriculumDetails   String?   @db.Text
  sampleDailySchedule String?   @db.Text
  priceOptions        Json     // [{ type: 'Shared Twin', price: 1800 }, ...]
  inclusions          String[]
  exclusions          String[]
  upcomingDates       Json     // [{ start: '...', end: '...', status: 'Open' }]
  applicationProcess  String?   @db.Text
}

model Retreat {
  id                  String   @id @default(cuid())
  chatbotId           String
  chatbot             Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  name                String
  theme               String?
  duration            String?
  intendedAudience    String?
  highlights          String[]
  priceOptions        Json
  upcomingDates       Json
}

model Policy {
  id                            String   @id @default(cuid())
  chatbotId                     String   @unique
  chatbot                       Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  codeOfConduct                 String?   @db.Text
  paymentPolicy                 String?   @db.Text
  cancellationAndRefundPolicy   String?   @db.Text
}

model FAQ {
  id          String   @id @default(cuid())
  chatbotId   String
  chatbot     Chatbot  @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  question    String
  answer      String   @db.Text
}


// ===================================
// 4. Chat History & Visitor Models
// (Unchanged from previous versions)
// ===================================

model ChatSession {
  id            String    @id @default(cuid())
  chatbotId     String
  chatbot       Chatbot   @relation(fields: [chatbotId], references: [id], onDelete: Cascade)
  visitorId     String
  visitor       Visitor   @relation(fields: [visitorId], references: [id])
  controller    String    @default("LLM") // Can be "LLM" or "USER"
  ablyChannel   String    @unique
  tokenCount    Int       @default(0)

  messages      Message[]
  createdAt     DateTime  @default(now())
}

model Message {
  id            String      @id @default(cuid())
  chatSessionId String
  chatSession   ChatSession @relation(fields: [chatSessionId], references: [id], onDelete: Cascade)
  senderType    String      // "VISITOR", "LLM", or "USER"
  content       String      @db.Text
  systemData    Json?       // For debugging: stores raw LLM tool calls, prompts, etc.
  createdAt     DateTime    @default(now())
}

model Visitor {
  id           String        @id @default(cuid())
  email        String?
  name         String?
  profileData  Json?
  chatSessions ChatSession[]
}

---

### **Part 1: The Zero-to-One Foundation**

**Goal:** To take the project from an empty folder to a functioning, secure application with core administrative capabilities.

#### **Sprint 0: Project Initialization & The Skeleton**

**(Goal: A secure, structured, and styled application shell. No dynamic functionality yet.)**

*   **Feature 0.1: Project Setup**
    *   **Action:** In your terminal, run `npx create-next-app@latest yogabot-live --typescript --tailwind --eslint`. This creates your project with modern defaults.
    *   **Action:** Run `cd yogabot-live` and `git init`. Create a new private repository on GitHub and push your initial commit.
    *   **Action:** Install core layout and UI packages: `npm install @radix-ui/react-slot class-variance-authority clsx tailwind-merge lucide-react`.

*   **Feature 0.2: The Full Prisma Schema**
    *   **Description:** This is the single source of truth for your data. Implementing it now prevents database changes later.
    *   **Action:** In your terminal, run `npx prisma init`.
    *   **Action:** Open `/prisma/schema.prisma`. Copy and paste the *entire* schema from your project document.
    *   **Action:** In your terminal, run `npx prisma migrate dev --name "v1-initial-schema"`. This command will generate the SQL to create all the tables, columns, and relations in your Vercel Postgres database.
    *   **Commit:** `feat: Implement full initial database schema`

*   **Feature 0.3: Core Authentication Setup**
    *   **Description:** We will set up the login system and the fundamental security layer.
    *   **Action:** Install dependencies: `npm install next-auth @next-auth/prisma-adapter bcryptjs`.
    *   **Action:** Create the file `/app/api/auth/[...nextauth]/route.ts`. This is the heart of your authentication.
    *   **`route.ts` Logic:**
        *   Import necessary modules: `NextAuth`, `PrismaAdapter`, `CredentialsProvider`.
        *   Instantiate the Prisma Adapter: `adapter: PrismaAdapter(prisma)`.
        *   Configure a `CredentialsProvider`. Its `authorize` function will:
            1.  Receive `email` and `password` from the login form.
            2.  Find a `User` in the database with the provided email.
            3.  If a user is found, use `bcryptjs.compare()` to check if the provided password matches the hashed password in the database.
            4.  If they match, return the `user` object. If not, return `null`.
        *   Configure `session` strategy to `jwt`.
        *   In the `callbacks`, ensure the `session` callback adds the `user.id` and `user.role` to the session token so this data is available throughout the app.

*   **Feature 0.4: The Protected Layout**
    *   **Description:** Create the main application shell with a sidebar and header, which will be the container for all authenticated pages. This prevents UI flicker and centralizes your layout logic.
    *   **Action:** Create a login page at `/app/login/page.tsx`. This will be a simple form with email and password fields.
    *   **Action:** Create a layout file at `/app/dashboard/(protected)/layout.tsx`.
        *   This layout will be a Server Component. It will fetch the user's session. If no session exists, it will `redirect('/login')`.
        *   It will render a static sidebar (for future navigation) and a header. The main content of the pages will be rendered via the `children` prop.
    *   **Action:** Create a placeholder page at `/app/dashboard/page.tsx` that simply says "Welcome to your Dashboard."
    *   **Action:** Create the `/middleware.ts` file in your root directory. Configure it to protect the `/dashboard/:path*` and `/api/:path*` routes (excluding the auth, public, and webhook routes).
    *   **Commit:** `feat: Implement authentication and protected dashboard layout`

#### **Sprint 1: The Admin Citadel**

**(Goal: To build the essential tools for YOU, the Admin, to manage the entire platform, including user-specific chatbot creation and settings overrides.)**

*   **Feature 1.1: Admin API Authorization Utility**
    *   **Description:** A reusable function to protect all admin-only API endpoints.
    *   **Action:** Create a file `/lib/admin-auth.ts`.
    *   **`admin-auth.ts` Logic:**
        ```typescript
        import { getServerSession } from "next-auth/next";
        import { authOptions } from "@/app/api/auth/[...nextauth]/route"; // Adjust path

        export async function verifyAdmin() {
          const session = await getServerSession(authOptions);
          if (session?.user?.role !== 'ADMIN') {
            throw new Error("Not authorized");
          }
          return { user: session.user };
        }
        ```
    *   **How to use:** At the start of every admin API route, you will call `await verifyAdmin()`. This will throw an error and stop execution if the user is not an admin.

*   **Feature 1.2: Admin Plan Management**
    *   **Description:** A full CRUD interface for managing subscription `Plan`s.
    *   **Backend First:**
        *   **Action:** Create API route `/app/api/admin/plans/route.ts` for `GET` (list all plans) and `POST` (create a plan).
        *   **Action:** Create API route `/app/api/admin/plans/[planId]/route.ts` for `PUT` (update a plan) and `DELETE` (delete a plan).
        *   **Logic:** Each function in these routes will first call `await verifyAdmin()`. They will use `zod` to validate the request body against the `Plan` model's structure. Then, they will use the Prisma client to perform the database operation.
    *   **Frontend Next:**
        *   **Action:** Create the page `/app/dashboard/admin/plans/page.tsx`.
        *   **UI/UX:** The page will fetch and display all plans in a table. A "Create New Plan" button will open a form to create/edit a plan. This form must include inputs for `name`, `price`, and all fields within the `features` JSON object, such as `chatbotLimit`, `tokenLimit`, `kbType`, and `canUseBYOK`.
    *   **Commit:** `feat: Implement full CRUD for subscription plans`

*   **Feature 1.3: Admin User & Subscription Onboarding**
    *   **Description:** A form for an Admin to create a new `User` and their `Subscription` without creating a chatbot yet. Chatbot creation is now a separate step.
    *   **Backend First:**
        *   **Action:** Create API route `/app/api/admin/onboard-user/route.ts`.
        *   **`onboard-user` Logic:**
            1.  `await verifyAdmin()`.
            2.  Use `zod` to validate: `email`, `name`, `password`, `planId`.
            3.  Hash the password using `bcryptjs`.
            4.  In a Prisma transaction, create the `User` and their `Subscription` record, linking them to the chosen `planId`.
    *   **Frontend Next:**
        *   **Action:** Create the page `/app/dashboard/admin/users/page.tsx`.
        *   **UI/UX:** The page will display a list of all current users. An "Onboard New User" button will open a form with fields for User Email, Name, Password, and a dropdown menu to select a `Plan`.
    *   **Commit:** `feat: Implement admin user and subscription creation`

*   **NEW Feature 1.4: Admin Chatbot Management**
    *   **Description:** A dedicated interface for an Admin to manage all chatbots associated with a specific user, create new ones within their plan limits, and override key settings.
    *   **Action:** Create a new page at `/app/dashboard/admin/users/[userId]`. This page will be linked from the user list in the feature above.
    *   **UI/UX & Logic:**
        1.  The page will display the selected user's details and a list of their current chatbots.
        2.  It will feature a **"Create New Chatbot"** button. This button should be disabled with a tooltip if `user.chatbots.length >= user.subscription.plan.features.chatbotLimit`.
        3.  Clicking "Create New Chatbot" will call an API to create a new default `Chatbot` record linked to this user.
        4.  Each chatbot in the list will have an "Edit" button that navigates to a new page `/app/dashboard/admin/chatbots/[chatbotId]`.
        5.  This chatbot-specific admin page is where you will set the overrides. It will have a form to edit:
            *   Approved Domain.
            *   **KB Type Override:** A dropdown menu with options ("(Use Plan Default)", "simple", "structured").
            *   **Simple KB Character Limit:** A number input field. This field should only be visible if the KB Type is set to "simple".
    *   **Commit:** `feat: Implement chatbot management and settings override for admins`

---

### **Part 2: The Core Product Experience**

**Goal:** To build the primary features that a paying user will interact with: populating their knowledge base and seeing the chat widget work.

#### **Sprint 2: The Knowledge Engine**

**(Goal: To allow users to input their business knowledge via the two distinct KB strategies, controlled by plan or admin override, and to process the structured data for Full-Text Search.)**

*   **Feature 2.1: The "Dual KB" Dashboard**
    *   **Description:** A single page that intelligently adapts to the selected chatbot's configuration, showing the user the correct Knowledge Base interface.
    *   **Action:** Create the page component at `/app/dashboard/kb/page.tsx`.
    *   **UI/UX & Logic (Server Component):**
        1.  The UI must include a dropdown menu at the top, allowing the user to select which of their chatbots they want to edit.
        2.  Once a chatbot is selected, the component will determine the effective KB type using this logic:
            *   `const effectiveKbType = selectedChatbot.kbTypeOverride || user.subscription.plan.features.kbType;`
        3.  Based on `effectiveKbType`, it will conditionally render ONE of two client components: `<SimpleKBForm />` or `<StructuredKBForm />`, passing the full `selectedChatbot` object as a prop.

*   **Feature 2.2: The "Simple Text" KB Pipeline (No Embeddings)**
    *   **Description:** The complete flow for a chatbot configured to use the direct-to-prompt simple text method, with an enforceable character limit.
    *   **Backend First (API):**
        *   **Action:** Create API route `/app/api/kb/simple/route.ts`. This `POST` route will:
            1.  Get the session and `chatbotId`.
            2.  Fetch the chatbot record to get its `simpleKbCharacterLimit`.
            3.  Validate the incoming `text`. If `text.length` exceeds the `simpleKbCharacterLimit`, return a `400 Bad Request` with an informative error message (e.g., "Knowledge base text exceeds the character limit for this bot.").
            4.  If valid, save the text directly: `prisma.chatbot.update({ where: { id: chatbotId }, data: { simpleKbText: text } })`.
            5.  **This API DOES NOT trigger any background job.**
    *   **Frontend Next:**
        *   **Action:** Build the `<SimpleKBForm />` client component.
        *   **UI/UX:** The textarea should load the chatbot's existing `simpleKbText`. It must display a live character count (e.g., `1234 / 5000`) based on the `simpleKbCharacterLimit` passed in the `chatbot` prop. The "Save" button should be disabled if the limit is exceeded.
    *   **Commit:** `feat: Implement simple text KB with character limits`

*   **Feature 2.3: The "Structured Form" KB Pipeline (FTS-Based)**
    *   **Description:** The advanced KB for chatbots configured to use Full-Text Search. This involves saving the data and processing it for keyword-based retrieval.
    *   **Backend First (API & Background Job):**
        *   **Action:** Create API route `/app/api/kb/structured/route.ts`. This API's logic is to save the structured data to the respective tables (`SchoolBrand`, `Teacher`, etc.) and then trigger the `process-kb` background job.
        *   **Action:** Create/Modify the background job handler at `/app/api/queues/process-kb/route.ts`.
        *   **Job Logic:**
            1.  Verify the request is from QStash.
            2.  Read all structured data for the given `chatbotId`.
            3.  Create human-readable text chunks from the data.
            4.  `await prisma.knowledgeBaseChunk.deleteMany({ where: { chatbotId } });`
            5.  `await prisma.knowledgeBaseChunk.createMany({ data: [...] });` to save the new chunks with their `content`.
            6.  After creating the chunks, run a raw query to populate the `tsvector` column for all the new chunks. This must be done as a separate step after creation.
                ```typescript
                await prisma.$executeRaw`
                  UPDATE "KnowledgeBaseChunk"
                  SET content_tsvector = to_tsvector('english', content)
                  WHERE "chatbotId" = ${chatbotId}
                `;
                ```
    *   **Frontend Next:**
        *   **Action:** Build the `<StructuredKBForm />` client component. (The UI for this component is unchanged from the original plan).
    *   **Commit:** `feat: Implement structured KB pipeline with FTS processing`

#### **Sprint 3: The First Conversation**

**(Goal: To connect the chat widget to the knowledge base, enabling intelligent conversations that respect the dynamically configured KB strategy—either Direct-to-Prompt or Full-Text Search—for that specific chatbot.)**

*   **Feature 3.1: The Embeddable Chat Widget**
    *   **Description:** The user-facing chat interface that will live on your clients' websites.
    *   **Action:** Create the page `/app/widget/[botId]/page.tsx`.
    *   **Logic & Security:**
        1.  This is a Server Component. In it, get the `Referer` from the `headers()`.
        2.  Fetch the `Chatbot` record using `botId`.
        3.  **Domain Lock:** If `Referer` does not start with `chatbot.approvedDomain`, return a `403 Forbidden` error page.
        4.  If it's valid, fetch the `widgetConfig` from the `Chatbot` record.
        5.  Render the `<ChatClient />` component, passing the `botId` and `widgetConfig` as props.
    *   **Action:** Build the `<ChatClient />` component. This will manage the chat state (messages, input text, loading status). It will have a message display area, a text input, and a send button.
    *   **Commit:** `feat: Build embeddable chat widget with domain locking`

*   **Feature 3.2: The Core Intelligent Chat API**
    *   **Description:** The brain of the chatbot. This API now dynamically chooses its retrieval strategy based on the chatbot's specific configuration.
    *   **Action:** Create the API route `/app/api/chat/send/route.ts`.
    *   **API Logic (The Dynamic Pipeline):**
        1.  **Usage Check & Get Body:** Perform middleware checks for subscription status and token limits. Parse the request body for `visitorId`, `message`, etc.
        2.  **Determine KB Strategy:**
            *   Fetch the `Chatbot` record and its related `User`, `Subscription`, and `Plan` data using the `botId` from the request.
            *   `const effectiveKbType = chatbot.kbTypeOverride || chatbot.user.subscription.plan.features.kbType;`
        3.  **Execute Strategy to Retrieve Context:** Use an `if/else` block based on `effectiveKbType`.
            *   **If `effectiveKbType === 'structured'`:**
                a. **Perform Full-Text Search:** Take the visitor's raw `message` string. **DO NOT USE THE EMBEDDING MODEL.**
                b. Prepare the search query for PostgreSQL's `to_tsquery` function (which accepts `&` for AND, `|` for OR). A simple approach is to join words with `&`.
                   ```typescript
                   const searchQuery = visitorMessage.trim().split(/\s+/).join(' & ');
                   ```
                c. Execute a raw Prisma query to find the most relevant chunks using FTS.
                   ```typescript
                   const results = await prisma.$queryRaw`
                     SELECT content, ts_rank(content_tsvector, to_tsquery('english', ${searchQuery})) as rank
                     FROM "KnowledgeBaseChunk"
                     WHERE "chatbotId" = ${chatbotId}
                     AND to_tsquery('english', ${searchQuery}) @@ content_tsvector
                     ORDER BY rank DESC
                     LIMIT 5;
                   `;
                   const context = results.map(r => r.content).join('\n---\n');
                   ```
            *   **If `effectiveKbType === 'simple'`:**
                a. **Fetch Direct Context:** The context is simply the `simpleKbText` from the chatbot record.
                   ```typescript
                   const context = chatbot.simpleKbText || "";
                   ```        4.  **Assemble Final Prompt:** Combine the retrieved `context` with the other prompt elements.
            *   `System Prompt -> Persona -> Retrieved Context -> Chat History -> Visitor Question`.
        5.  **LLM Call:** Send this fully assembled prompt to your Gemini LLM adapter.
        6.  **Save Everything:** Save the user's message and the LLM's response to the database. Atomically increment the `tokensUsedThisPeriod` on the user's `Subscription`.
        7.  Return the LLM's response to the chat widget.
    *   **Commit:** `feat: Implement dynamic core chat API with FTS and simple KB strategies`

---

### **Part 3: Advanced Capabilities & The Human Touch**

**Goal:** To elevate the product from a simple RAG chatbot to a sophisticated tool with advanced AI capabilities and seamless human-computer collaboration.

#### **Sprint 4: The Intelligent Agent (Tool Calling)**

**(Goal: To give the chatbot superpowers by allowing it to use tools to answer questions it cannot answer from the static knowledge base alone, like "Are there any spots left in the upcoming 200-hour TTC?")**

*   **Feature 4.1: Define and Implement a Tool Kit**
    *   **Description:** We will create a library of "tools" the LLM can decide to use. A tool is just a server-side function that the LLM can ask our application to run.
    *   **Action (Backend):** Create a new file `/lib/llm-tools.ts`.
    *   **Logic:**
        *   Define the functions themselves. These functions will query your Prisma models. For example:
            ```typescript
            // In /lib/llm-tools.ts
            export async function getUpcomingTTCs(chatbotId: string) {
              // Prisma logic to find TTCs where upcomingDates are in the future
            }
            export async function getTeacherBio(chatbotId:string, teacherName: string) {
              // Prisma logic to find a teacher by name
            }
            ```
        *   Define the JSON schema for each tool. This is the "menu" you show to the LLM.
            ```typescript
            // Also in /lib/llm-tools.ts
            export const toolSchemas = [
              {
                name: 'getUpcomingTTCs',
                description: 'Get a list of all upcoming Yoga Teacher Training Courses.',
                parameters: { type: 'object', properties: {} } // No params needed
              },
              {
                name: 'getTeacherBio',
                description: 'Get the detailed biography for a specific teacher.',
                parameters: { type: 'object', properties: { teacherName: { type: 'string' } }, required: ['teacherName'] }
              }
            ];
            ```

*   **Feature 4.2: Upgrade the Chat API to a Tool-Using Agent**
    *   **Description:** We will modify the core chat API to manage a multi-step conversation with the LLM when it decides to use a tool.
    *   **Action (Backend):** Modify the API route at `/app/api/chat/send/route.ts`.
    *   **New API Logic:**
        1.  The RAG pipeline (semantic search, prompt assembly) remains the same.
        2.  **LLM Call (Modification):** When you call the Gemini API, now include the `toolSchemas` in the request.
        3.  **Handle the Response (New Logic):** The LLM can now respond in two ways:
            *   **A) Text Response:** If it's a simple text response, the process is the same as before. Save and return the message.
            *   **B) Tool Call Response:** If the LLM responds with a `tool_call` object:
                a. **Do not** send this technical response to the user.
                b. Parse the `tool_call` to get the function name (e.g., `getTeacherBio`) and the arguments (e.g., `{ teacherName: 'Ananda' }`).
                c. Look up the corresponding function in your `/lib/llm-tools.ts` and execute it.
                d. **Make a second call to the LLM.** This time, you send the *result* of your function back to the LLM as part of the conversation history.
                e. The LLM will now receive this data and formulate a natural language response (e.g., "Yes, I found the bio for Ananda. She is a certified...").
                f. Save this final text response to the database and send it to the user.
    *   **Commit:** `feat: Implement tool-calling agent in chat API`

#### **Sprint 5: The "Live" in YogaBot Live**

**(Goal: To implement the real-time monitoring dashboard and human takeover functionality, connecting users directly with their website visitors.)**

*   **Feature 5.1: Secure Real-Time Backend**
    *   **Description:** Setting up the secure authentication flow for the real-time service (Ably). The master API key must never be exposed to the client.
    *   **Action:** Install the dependency: `npm install ably`.
    *   **Action (Backend):** Create an API route at `/app/api/ably/token/route.ts`.
    *   **Logic:**
        1.  Get the user's session to identify them.
        2.  Instantiate the Ably REST client using your master `ABLY_API_KEY`.
        3.  Use `ably.auth.createTokenRequest()` to generate a temporary, short-lived token for the authenticated user. This token grants them permission to subscribe to specific channels (e.g., `chatbot-ID:*`).
        4.  Return this token to the client.

*   **Feature 5.2: The Live Monitor Dashboard**
    *   **Description:** A new dashboard page where your users can see active chats in real-time.
    *   **Action (Frontend):** Create the page `/app/dashboard/live/page.tsx`.
    *   **UI/UX:**
        *   The page will have two panels. The left panel is a list of "Active Visitors". The right panel will display the selected conversation.
        *   On page load, it calls `/api/ably/token` to get a token and initializes the Ably client-side SDK.
        *   **Presence:** Use Ably's Presence feature on a channel like `live-monitor:{chatbotId}`. The chat widget will `enter` this channel, and the dashboard will subscribe to presence events to dynamically update the list of active visitors.
        *   When an admin clicks a visitor in the list, the app subscribes to that specific chat's channel (e.g., `chat:{chatSessionId}`) and loads the message history. New messages will appear in real-time.

*   **Feature 5.3: The Takeover & Human Chat Logic**
    *   **Description:** Implementing the backend logic that allows a human to pause the AI and take control of the conversation.
    *   **Action (Backend):**
        *   Create an API route `/app/api/chat/takeover/route.ts`. This `POST` route takes a `chatSessionId` and updates the `controller` field on the `ChatSession` model to `"USER"`. It then publishes a message via Ably to the widget, disabling the visitor's input temporarily with a message like "Please wait, connecting you to a team member...".
        *   Create an API route `/app/api/chat/send-human/route.ts`. This route takes `chatSessionId` and `message`, saves the message to the database with `senderType: "USER"`, and publishes the message to the chat channel for the widget to display.
    *   **Action (Frontend):** In the Live Monitor UI, add a "Take Over" button. When clicked, it calls the takeover API. The input box is then enabled, and sending a message calls the `send-human` API.
    *   **Modification to AI Chat API:** Modify `/api/chat/send/route.ts` one last time. At the very beginning, it must fetch the `ChatSession` and check the `controller` field. If it's `"USER"`, it must stop immediately and not call the LLM.
    *   **Commit:** `feat: Implement real-time chat monitor and human takeover`

---

### **Part 4: Commercialization & Professional Polish**

**Goal:** To turn the application into a legitimate, automated business and add the final touches that create a professional user experience.

#### **Sprint 6: The Business Engine (Billing)**

**(Goal: To fully automate the subscription and payment process using Razorpay.)**

*   **Feature 6.1: Public API for Plans**
    *   **Description:** Create a public, unauthenticated API endpoint that the separate marketing website can use to fetch plan details for its pricing page.
    *   **Action (Backend):** Create a new API route: `/app/api/public/plans/route.ts`.
    *   **Logic:** This `GET` route will connect to the database and return all `Plan` records where `isActive` is `true`. It will only return data relevant for a pricing page (e.g., `id`, `name`, `price`, `features`). Remember to configure CORS to allow requests from your marketing website's domain.

*   **Feature 6.2: Subscription & Checkout Flow**
    *   **Description:** Handle a user coming from the marketing website to sign up for a specific plan and complete the payment.
    *   **Action (Frontend):** Create a sign-up page at `/app/signup/page.tsx`. This page should be able to read a `planId` from the URL query parameters (e.g., `?planId=pro_plan_123`).
    *   **Action (Backend):** Create an API route `/app/api/billing/create-subscription/route.ts`. This protected route will take a `planId`, use the Razorpay SDK to create a subscription, and return the `subscription_id`.
    *   **User Flow:**
        1.  A user lands on `/signup?planId=pro_plan_123`.
        2.  They fill out the form to create a `User` account.
        3.  Upon successful account creation, the application immediately calls `/api/billing/create-subscription` with the `planId`.
        4.  The application uses the returned `subscription_id` to open the Razorpay Checkout form, allowing the user to pay.

*   **Feature 6.3: The Critical Billing Webhook**
    *   **Description:** This is the most critical piece of the billing system. It's an automated listener that keeps your database in sync with Razorpay's payment events.
    *   **Action (Backend):** Create the webhook handler at `/app/api/webhooks/razorpay/route.ts`.
    *   **Logic:**
        1.  **Security First:** The very first step is to cryptographically verify the webhook signature sent by Razorpay. If the signature is invalid, return a `400 Bad Request` immediately.
        2.  **Event Handling:** Use a `switch` statement on the `event.type` from the webhook payload.
            *   **Case `subscription.charged`:** The payment was successful. Find the `Subscription` in your database via the `razorpaySubscriptionId`. Update its `status` to `"active"` and set the `currentPeriodEnd` based on the data from Razorpay.
            *   **Case `subscription.halted` or `payment.failed`:** The payment failed. Find the `Subscription` and update its `status` to `"past_due"` or `"cancelled"`. The middleware will then automatically block them from using token-consuming features.
        3.  Return a `200 OK` response to Razorpay to acknowledge receipt of the webhook.
    *   **Commit:** `feat: Implement Razorpay billing and webhook handler`

#### **Sprint 7: Value-Add & Final Polish**

**(Goal: To add the final features that deliver key business value, improve the user experience, and create a complete product.)**

*   **Feature 7.1: User Settings & Security**
    *   **Description:** A dedicated page for users to manage their settings.
    *   **Action:** Create the page `/app/dashboard/settings/page.tsx`.
    *   **UI/UX & Logic:**
        *   **Widget Config:** A form to edit the `widgetConfig` JSON object (e.g., colors, welcome message).
        *   **SMTP Config:** A form to securely save their SMTP credentials for email forwarding. **Crucially**, when you save this data via your API, you must use `Node.js crypto` (AES-256-GCM) to encrypt the password using your `ENCRYPTION_KEY` before storing it. Decrypt it only when you need to use it.
        *   **BYOK (Bring Your Own Key):** This section should only be rendered if the user's `plan.features.canUseBYOK` is true. It will have an input for their LLM API key, which must also be encrypted in the database just like the SMTP password.

*   **Feature 7.2: Automated Email System**
    *   **Description:** Sending automated, professional emails for key events.
    *   **Action:** Install a library for creating beautiful emails: `npm install react-email resend`.
    *   **Action:** Create your email templates as React components in a new `/emails` directory (e.g., `WelcomeEmail.tsx`, `PasswordReset.tsx`, `SubscriptionWarning.tsx`).
    *   **Action:** Integrate these into your application logic:
        *   After a user is successfully onboarded, trigger the welcome email.
        *   When a user's `tokensUsedThisPeriod` crosses 90% of their `plan.features.tokenLimit`, trigger the subscription warning email.
        *   Build a standard "forgot password" flow that uses the password reset email template.

*   **Feature 7.3: The Leads Dashboard**
    *   **Description:** This is a dedicated UI for your users to view all the visitor contact information collected by their chatbot in a clean, CRM-like interface, completely separate from the chat logs. This provides immediate business value and is a key feature for marketing and sales-focused users.
    *   **Backend First (The Leads API):**
        *   **Action:** Create a new, dedicated API route at `/app/api/leads/route.ts`.
        *   **Logic:**
            1.  Get the user's session and their `chatbotId`.
            2.  Perform a Prisma query to fetch all `Visitor` records that have at least one `ChatSession` associated with the user's `chatbotId`.
            3.  For each `Visitor`, you can also query to find the date of their first `ChatSession` to show a "Date Captured" field.
            4.  The API should return a clean array of lead objects, for example: `[{ id, name, email, profileData, firstSeenAt }, ...]`.
    *   **Frontend Next (The UI):**
        *   **Action:** Create a new page at `/app/dashboard/leads/page.tsx`. Add a link to it in your main dashboard navigation sidebar.
        *   **UI/UX:**
            *   The page will display the leads in a clean, sortable table.
            *   Columns should include: `Name`, `Email`, `Other Info` (from `profileData`), and `Date Captured`.
            *   Include a search bar to filter leads by name or email.
            *   **Future-Proofing for Integrations:** Add a prominent **"Export to CSV"** button. This is a simple but powerful feature that immediately allows your users to export their leads and upload them to any external CRM or email marketing service (like Mailchimp), fulfilling the integration use case from day one with minimal development effort.

*   **Feature 7.4: Create the Marketing API Note**
    *   **Description:** A final task to create a clear hand-off document for the team/person building the separate marketing website.
    *   **Action:** In the root of your project, create a new file named `note_for_marketing.md`.
    *   **Content of the Note:** This document will describe the public interface of `app.yogabot.live`.
        ```markdown
        # Interfacing with the YogaBot Live Application

        This document outlines the public endpoints and URLs provided by the main application (`app.yogabot.live`) for use by the marketing website.

        ## Public API Endpoints

        ### `GET /api/public/plans`

        *   **Purpose:** Fetches all active subscription plans to be displayed on the pricing page.
        *   **Method:** `GET`
        *   **URL:** `https://app.yogabot.live/api/public/plans`
        *   **Response:** A JSON array of plan objects.
            ```json
            [
              {
                "id": "plan_id_123",
                "name": "Pro",
                "price": 4900,
                "features": { "kbType": "structured", "tokenLimit": 50000, ... }
              }
            ]
            ```

        ## User Entry Point URLs

        *   **Login URL:** To send existing users to log in, link to:
            `https://app.yogabot.live/login`

        *   **Signup & Subscribe URL:** To send new users to sign up for a specific plan, link to:
            `https://app.yogabot.live/signup?planId=<PLAN_ID_FROM_API>`
            (Replace `<PLAN_ID_FROM_API>` with the actual `id` of the chosen plan).
        ```
    *   **Commit:** `docs: Create marketing API integration note`