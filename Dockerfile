# ====================================================================================
#  Dockerfile for building a custom PostgreSQL image with pgvector
# ====================================================================================

# Step 1: Start from the official, trusted PostgreSQL 15 image.
FROM postgres:15

# Step 2: Run commands as the root user to install new software.
USER root

# Step 3: Update the package lists and install the pgvector package.
# The 'postgresql-15-pgvector' package is specifically built for Postgres 15.
# '-y' automatically answers "yes" to any installation prompts.
RUN apt-get update && apt-get install -y postgresql-15-pgvector