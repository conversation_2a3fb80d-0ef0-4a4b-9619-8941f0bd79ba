import { withAuth } from "next-auth/middleware"

export default with<PERSON>uth(
  function middleware(req) {
    // Add any additional middleware logic here if needed
  },
  {
    callbacks: {
      authorized: ({ token, req }) => {
        const { pathname } = req.nextUrl

        // Allow access to public routes
        if (pathname.startsWith("/login") || pathname.startsWith("/api/auth")) {
          return true
        }

        // Protect dashboard routes - require authentication
        if (pathname.startsWith("/dashboard") || pathname.startsWith("/api")) {
          return !!token
        }

        // Allow access to other routes
        return true
      },
    },
  }
)

export const config = {
  matcher: [
    "/dashboard/:path*",
    "/api/:path*",
    "/login"
  ]
}
